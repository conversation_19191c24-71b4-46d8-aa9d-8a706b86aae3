# Especificação Técnica - Sistema de Certificados

## 📋 Visão Geral

Implementar sistema completo de certificados em PDF com frente, verso e ementa do curso anexada automaticamente. O sistema deve permitir emissão automática e manual, com integração ao Supabase para armazenamento.

## 🎯 Objetivos

- Gerar certificados em PDF com 3 páginas: frente + verso + ementa
- Emissão automática na conclusão do curso
- Emissão manual pelo professor/admin
- Armazenamento seguro no Supabase Storage
- Download seguro com URLs assinadas
- Integração com sistema existente de configuração

## 🏗️ Arquitetura Atual

### ✅ Já Implementado

1. **Configuração de Certificados**
   - Arquivo: `apps/web/app/[locale]/(platform)/app/products/components/tabs/CertificatesTab.tsx`
   - Schema de validação completo com Zod
   - Interface para configurar templates
   - Três abas: Design, Conteúdo, Requisitos

2. **Estrutura de Dados**
   - `Product.certificateTemplate` (JSON) - configurações do template
   - `Course.certificateEnabled` (Boolean) - certificado habilitado
   - `Course.modules` (JSON) - estrutura do curso para ementa
   - `CourseEnrollment.completedAt` - data de conclusão

3. **Sistema de Progresso**
   - Cálculo automático de progresso por módulo/aula
   - Critérios de conclusão configuráveis
   - Tracking de progresso individual

### 📁 Assets Existentes

- `_certificados/frente.png` - Template visual da frente
- `_certificados/verso.png` - Template visual do verso
- `_certificados/modelo.pdf` - Modelo atual (2 páginas)

## 🚀 Implementação Necessária

### 1. Configuração do Supabase

#### 1.1 Setup do Projeto
```bash
# Instalar dependências
npm install @supabase/supabase-js
npm install @supabase/storage-js
```

#### 1.2 Configuração de Environment
```env
# .env.local
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

#### 1.3 Cliente Supabase
```typescript
// packages/storage/provider/supabase.ts
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export const supabase = createClient(supabaseUrl, supabaseKey)
```

#### 1.4 Storage Bucket
```sql
-- Criar bucket no Supabase
INSERT INTO storage.buckets (id, name, public)
VALUES ('certificates', 'certificates', false);

-- Políticas de acesso
CREATE POLICY "Users can view own certificates" ON storage.objects
FOR SELECT USING (bucket_id = 'certificates' AND auth.uid()::text = (storage.foldername(name))[1]);
```

### 2. Modelo de Dados

#### 2.1 Schema Prisma
```prisma
// packages/database/prisma/schema.prisma
model Certificate {
  id          String   @id @default(cuid())
  userId      String
  productId   String
  courseId    String?
  fileUrl     String   // URL no Supabase Storage
  fileName    String   // Nome do arquivo
  issuedAt    DateTime @default(now())
  issuedBy    String?  // ID do usuário que emitiu (para emissão manual)
  status      CertificateStatus @default(ACTIVE)
  metadata    Json?    // Dados adicionais do certificado

  user        User     @relation(fields: [userId], references: [id])
  product     Product  @relation(fields: [productId], references: [id])
  course      Course?  @relation(fields: [courseId], references: [id])

  @@unique([userId, productId])
  @@index([userId])
  @@index([productId])
  @@map("certificates")
}

enum CertificateStatus {
  ACTIVE
  REVOKED
  EXPIRED
}
```

#### 2.2 Migração
```bash
npx prisma migrate dev --name add_certificates
npx prisma generate
```

### 3. Gerador de PDF

#### 3.1 Dependências
```bash
npm install jspdf html2canvas
npm install @types/jspdf
```

#### 3.2 Serviço de Geração
```typescript
// packages/api/modules/certificates/services/pdf-generator.ts
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

interface CertificateData {
  studentName: string
  courseName: string
  completionDate: Date
  duration: number
  instructor: string
  template: any
  syllabus: CourseSyllabus
}

interface CourseSyllabus {
  modules: Array<{
    title: string
    description?: string
    lessons: Array<{
      title: string
      duration: number
      type: string
    }>
  }>
  objectives: string[]
  totalDuration: number
}

export class CertificatePDFGenerator {
  async generateCertificate(data: CertificateData): Promise<Buffer> {
    const pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    })

    // Página 1: Frente do certificado
    await this.addFrontPage(pdf, data)

    // Página 2: Verso do certificado
    pdf.addPage()
    await this.addBackPage(pdf, data)

    // Página 3+: Ementa do curso
    pdf.addPage()
    await this.addSyllabusPages(pdf, data.syllabus)

    return Buffer.from(pdf.output('arraybuffer'))
  }

  private async addFrontPage(pdf: jsPDF, data: CertificateData) {
    // Implementar renderização da frente
    // Usar template configurado + dados do aluno
  }

  private async addBackPage(pdf: jsPDF, data: CertificateData) {
    // Implementar renderização do verso
    // Informações adicionais, QR code, etc.
  }

  private async addSyllabusPages(pdf: jsPDF, syllabus: CourseSyllabus) {
    // Implementar renderização da ementa
    // Lista de módulos, aulas, objetivos
  }
}
```

### 4. APIs e Procedures

#### 4.1 Estrutura de Procedures
```typescript
// packages/api/modules/certificates/index.ts
export { generateCertificate } from './procedures/generate'
export { issueCertificate } from './procedures/issue'
export { downloadCertificate } from './procedures/download'
export { listCertificates } from './procedures/list'
export { checkEligibility } from './procedures/check-eligibility'
```

#### 4.2 Procedure de Geração
```typescript
// packages/api/modules/certificates/procedures/generate.ts
import { z } from 'zod'
import { protectedProcedure } from '../../../trpc/base'
import { CertificatePDFGenerator } from '../services/pdf-generator'
import { supabase } from '../../../../storage/provider/supabase'
import { db } from 'database'

export const generateCertificate = protectedProcedure
  .input(
    z.object({
      userId: z.string(),
      productId: z.string(),
      isManual: z.boolean().default(false)
    })
  )
  .mutation(async ({ input, ctx }) => {
    // 1. Verificar elegibilidade
    const eligibility = await checkUserEligibility(input.userId, input.productId)
    if (!eligibility.eligible) {
      throw new Error(eligibility.reason)
    }

    // 2. Buscar dados necessários
    const certificateData = await getCertificateData(input.userId, input.productId)

    // 3. Gerar PDF
    const generator = new CertificatePDFGenerator()
    const pdfBuffer = await generator.generateCertificate(certificateData)

    // 4. Upload para Supabase
    const fileName = `${input.userId}/${input.productId}/certificate-${Date.now()}.pdf`
    const { data: uploadData, error } = await supabase.storage
      .from('certificates')
      .upload(fileName, pdfBuffer, {
        contentType: 'application/pdf',
        upsert: true
      })

    if (error) throw error

    // 5. Salvar registro no banco
    const certificate = await db.certificate.create({
      data: {
        userId: input.userId,
        productId: input.productId,
        fileUrl: uploadData.path,
        fileName: fileName,
        issuedBy: input.isManual ? ctx.user.id : null,
        metadata: {
          generatedAt: new Date(),
          version: '1.0'
        }
      }
    })

    return certificate
  })
```

#### 4.3 Procedure de Download
```typescript
// packages/api/modules/certificates/procedures/download.ts
export const downloadCertificate = protectedProcedure
  .input(
    z.object({
      certificateId: z.string()
    })
  )
  .query(async ({ input, ctx }) => {
    // 1. Verificar propriedade
    const certificate = await db.certificate.findFirst({
      where: {
        id: input.certificateId,
        userId: ctx.user.id
      }
    })

    if (!certificate) {
      throw new Error('Certificado não encontrado')
    }

    // 2. Gerar URL assinada (24h)
    const { data, error } = await supabase.storage
      .from('certificates')
      .createSignedUrl(certificate.fileUrl, 86400) // 24 horas

    if (error) throw error

    return {
      downloadUrl: data.signedUrl,
      fileName: certificate.fileName,
      expiresAt: new Date(Date.now() + 86400 * 1000)
    }
  })
```

### 5. Extração de Ementa

#### 5.1 Serviço de Ementa
```typescript
// packages/api/modules/certificates/services/syllabus-extractor.ts
export class SyllabusExtractor {
  static extractFromCourse(course: any): CourseSyllabus {
    const modules = course.modules || []

    return {
      modules: modules.map((module: any) => ({
        title: module.title || 'Módulo sem título',
        description: module.description,
        lessons: (module.lessons || []).map((lesson: any) => ({
          title: lesson.title || 'Aula sem título',
          duration: lesson.duration || 0,
          type: lesson.type || 'VIDEO'
        }))
      })),
      objectives: course.learningObjectives || [],
      totalDuration: this.calculateTotalDuration(modules)
    }
  }

  private static calculateTotalDuration(modules: any[]): number {
    return modules.reduce((total, module) => {
      const moduleDuration = (module.lessons || []).reduce(
        (sum: number, lesson: any) => sum + (lesson.duration || 0),
        0
      )
      return total + moduleDuration
    }, 0)
  }
}
```

### 6. Interface do Usuário

#### 6.1 Componente de Certificado na Área do Aluno
```typescript
// apps/web/app/[locale]/(lms)/account/course/[courseId]/components/CertificateSection.tsx
import { api } from '@/lib/api'
import { Button } from '@ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card'
import { Download, Award } from 'lucide-react'

interface CertificateSectionProps {
  courseId: string
  isEligible: boolean
  hasCompletedCourse: boolean
}

export function CertificateSection({ courseId, isEligible, hasCompletedCourse }: CertificateSectionProps) {
  const { data: certificate } = api.certificates.listCertificates.useQuery(
    { productId: courseId },
    { enabled: hasCompletedCourse }
  )

  const downloadMutation = api.certificates.downloadCertificate.useMutation()

  const handleDownload = async () => {
    if (certificate?.[0]) {
      const result = await downloadMutation.mutateAsync({
        certificateId: certificate[0].id
      })

      // Abrir URL de download
      window.open(result.downloadUrl, '_blank')
    }
  }

  if (!isEligible) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Certificado
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Complete o curso para receber seu certificado.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Award className="h-5 w-5 text-yellow-500" />
          Certificado Disponível
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          Parabéns! Você concluiu o curso e pode baixar seu certificado.
        </p>

        <Button
          onClick={handleDownload}
          disabled={downloadMutation.isLoading}
          className="w-full"
        >
          <Download className="h-4 w-4 mr-2" />
          {downloadMutation.isLoading ? 'Preparando...' : 'Baixar Certificado'}
        </Button>

        {certificate?.[0] && (
          <p className="text-xs text-muted-foreground">
            Emitido em {new Date(certificate[0].issuedAt).toLocaleDateString('pt-BR')}
          </p>
        )}
      </CardContent>
    </Card>
  )
}
```

#### 6.2 Seção de Emissão Manual
```typescript
// apps/web/app/[locale]/(platform)/app/products/components/tabs/CertificateManagement.tsx
import { api } from '@/lib/api'
import { Button } from '@ui/components/button'
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card'
import { Badge } from '@ui/components/badge'

interface CertificateManagementProps {
  productId: string
}

export function CertificateManagement({ productId }: CertificateManagementProps) {
  const { data: eligibleStudents } = api.certificates.getEligibleStudents.useQuery({ productId })
  const issueMutation = api.certificates.issueCertificate.useMutation()

  const handleIssue = async (userId: string) => {
    await issueMutation.mutateAsync({
      userId,
      productId,
      isManual: true
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Gerenciar Certificados</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {eligibleStudents?.map((student) => (
            <div key={student.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <p className="font-medium">{student.name}</p>
                <p className="text-sm text-muted-foreground">{student.email}</p>
                <p className="text-xs text-muted-foreground">
                  Concluído em {new Date(student.completedAt).toLocaleDateString('pt-BR')}
                </p>
              </div>

              <div className="flex items-center gap-2">
                {student.hasCertificate ? (
                  <Badge variant="success">Emitido</Badge>
                ) : (
                  <Button
                    size="sm"
                    onClick={() => handleIssue(student.id)}
                    disabled={issueMutation.isLoading}
                  >
                    Emitir Certificado
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
```

### 7. Emissão Automática

#### 7.1 Hook de Conclusão
```typescript
// packages/api/modules/courses/procedures/complete-course.ts
import { generateCertificate } from '../certificates/procedures/generate'

export const completeCourse = protectedProcedure
  .input(
    z.object({
      courseId: z.string()
    })
  )
  .mutation(async ({ input, ctx }) => {
    // 1. Marcar curso como concluído
    const enrollment = await db.courseEnrollment.update({
      where: {
        userId_courseId: {
          userId: ctx.user.id,
          courseId: input.courseId
        }
      },
      data: {
        completedAt: new Date()
      }
    })

    // 2. Verificar se certificado está habilitado
    const course = await db.course.findUnique({
      where: { id: input.courseId },
      include: { product: true }
    })

    if (course?.certificateEnabled) {
      // 3. Gerar certificado automaticamente
      try {
        await generateCertificate.mutation({
          userId: ctx.user.id,
          productId: course.productId,
          isManual: false
        })
      } catch (error) {
        console.error('Erro ao gerar certificado:', error)
        // Não falhar a conclusão do curso por erro no certificado
      }
    }

    return enrollment
  })
```

### 8. Testes

#### 8.1 Testes Unitários
```typescript
// packages/api/modules/certificates/__tests__/pdf-generator.test.ts
import { CertificatePDFGenerator } from '../services/pdf-generator'

describe('CertificatePDFGenerator', () => {
  it('should generate PDF with 3 pages', async () => {
    const generator = new CertificatePDFGenerator()
    const mockData = {
      studentName: 'João Silva',
      courseName: 'Curso de Teste',
      completionDate: new Date(),
      duration: 120,
      instructor: 'Professor Teste',
      template: {},
      syllabus: {
        modules: [],
        objectives: [],
        totalDuration: 120
      }
    }

    const pdf = await generator.generateCertificate(mockData)
    expect(pdf).toBeInstanceOf(Buffer)
    expect(pdf.length).toBeGreaterThan(0)
  })
})
```

### 9. Deployment

#### 9.1 Variáveis de Ambiente
```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=

# Certificados
CERTIFICATE_STORAGE_BUCKET=certificates
CERTIFICATE_URL_EXPIRY=86400
```

#### 9.2 Scripts de Deploy
```bash
# Migração do banco
npx prisma migrate deploy

# Build da aplicação
npm run build

# Verificar configuração do Supabase
npm run verify:supabase
```

## 📋 Checklist de Implementação

### Fase 1: Setup Inicial ✅
- [x] Configurar Supabase projeto e storage
- [x] Criar modelo Certificate no Prisma
- [x] Executar migração do banco
- [x] Configurar variáveis de ambiente

### Fase 2: Geração de PDF ✅
- [x] Implementar CertificatePDFGenerator
- [x] Criar templates de frente e verso
- [x] Implementar geração de ementa
- [x] Testar geração de PDF

### Fase 3: APIs ✅
- [x] Criar procedures de certificados
- [x] Implementar upload para Vercel Storage
- [x] Criar sistema de URLs assinadas
- [x] Implementar verificação de elegibilidade

### Fase 4: Interface ✅
- [x] Criar CertificateSection para área do aluno
- [x] Implementar CertificateManagement para admin
- [x] Integrar com página do curso
- [x] Adicionar à aba de certificados

### Fase 5: Automação ✅
- [x] Implementar emissão automática
- [x] Criar hook de conclusão de curso
- [x] Configurar notificações
- [x] Implementar logs de auditoria

### Fase 6: Testes e Deploy 🔄
- [x] Escrever testes unitários
- [ ] Testar fluxo completo
- [ ] Configurar CI/CD
- [ ] Deploy em produção

## 🔧 Comandos Úteis

```bash
# Desenvolvimento
npm run dev
npm run db:studio
npm run db:migrate

# Testes
npm run test
npm run test:certificates

# Build
npm run build
npm run start

# Supabase
supabase start
supabase status
supabase storage ls
```

## 📚 Referências

- [Supabase Storage Documentation](https://supabase.com/docs/guides/storage)
- [jsPDF Documentation](https://github.com/parallax/jsPDF)
- [Prisma Documentation](https://www.prisma.io/docs)
- [tRPC Documentation](https://trpc.io/docs)

---

**Próximo Passo**: Iniciar com a Fase 1 (Setup Inicial) seguindo o checklist acima.
