



Vou analisar a estrutura atual da página de edição de produtos para entender como implementar a funcionalidade de certificados. Primeiro preciso examinar o arquivo mencionado e a estrutura do projeto.


toolName: view_files

status: success


filePath: /Users/<USER>/Documents/www/BuscarInovar/buscar-inovar-platform/apps/web/app/[locale]/(platform)/app/products/[id]/edit/page.tsx

Agora preciso examinar o componente cliente para entender a estrutura atual da página de edição.


toolName: view_files

status: success


filePath: /Users/<USER>/Documents/www/BuscarInovar/buscar-inovar-platform/apps/web/app/[locale]/(platform)/app/products/[id]/edit/page.client.tsx

Vou examinar o restante do arquivo para ver a estrutura completa do componente e como as abas estão organizadas.


toolName: view_files

status: success


filePath: /Users/<USER>/Documents/www/BuscarInovar/buscar-inovar-platform/apps/web/app/[locale]/(platform)/app/products/[id]/edit/page.client.tsx

Agora vou examinar a parte final do arquivo para ver como as abas são renderizadas e onde a aba de certificados está sendo usada.


toolName: view_files

status: success


filePath: /Users/<USER>/Documents/www/BuscarInovar/buscar-inovar-platform/apps/web/app/[locale]/(platform)/app/products/[id]/edit/page.client.tsx

Preciso ver a definição das abas (tabItems) para entender como a aba de certificados está configurada.


toolName: view_files

status: success


filePath: /Users/<USER>/Documents/www/BuscarInovar/buscar-inovar-platform/apps/web/app/[locale]/(platform)/app/products/[id]/edit/page.client.tsx

Agora vou examinar o componente CertificatesTab para entender a implementação atual.


toolName: view_files

status: success


filePath: /Users/<USER>/Documents/www/BuscarInovar/buscar-inovar-platform/apps/web/app/[locale]/(platform)/app/products/components/tabs/CertificatesTab.tsx

Vou examinar o restante do componente CertificatesTab para entender a implementação completa.


toolName: view_files

status: success


filePath: /Users/<USER>/Documents/www/BuscarInovar/buscar-inovar-platform/apps/web/app/[locale]/(platform)/app/products/components/tabs/CertificatesTab.tsx

Agora vou examinar a estrutura do banco de dados para entender os modelos relacionados a certificados.


toolName: search_codebase

status: success

query: database schema models related to certificates, course completion, and student progress

Agora vou examinar a área do aluno para entender como os certificados são exibidos atualmente.


toolName: search_codebase

status: success

query: student area certificate display, certificate generation, and certificate download functionality

Agora vou examinar as APIs relacionadas a certificados para entender como a geração e emissão funcionam.


toolName: search_codebase

status: success

query: certificate generation API, certificate issuance procedures, and certificate download endpoints

Agora vou buscar especificamente por APIs de certificados, procedures de geração e emissão de certificados.


toolName: search_by_regex

status: success

query: certificate|Certificate

Vou examinar a área do aluno para entender como os certificados são exibidos e acessados.


toolName: search_codebase

status: success

query: student area certificate display, certificate download, certificate access in student dashboard

Agora vou examinar a estrutura da área do aluno (LMS) para entender como os certificados são exibidos e acessados.


toolName: view_files

status: success


filePath: /Users/<USER>/Documents/www/BuscarInovar/buscar-inovar-platform/apps/web/app/[locale]/(lms)/account/course/[courseId]/page.tsx

Vou examinar o início da página do curso para entender melhor a estrutura e como o progresso é calculado.


toolName: view_files

status: success


filePath: /Users/<USER>/Documents/www/BuscarInovar/buscar-inovar-platform/apps/web/app/[locale]/(lms)/account/course/[courseId]/page.tsx

Agora vou buscar por APIs e procedures relacionados à geração e emissão de certificados.


toolName: search_by_regex

status: success

query: generateCertificate|issueCertificate|certificate.*generate|certificate.*issue

Vou criar uma lista de tarefas detalhada para implementar a funcionalidade de certificados com base na análise realizada.


toolName: todo_write

status: success

Todos updated: 6 items

# Análise Detalhada dos Requisitos para Implementação da Funcionalidade de Certificados

## 1. Estrutura Atual da Página de Edição de Produtos

### Componentes Identificados
- **Arquivo Principal**: <mcfile name="page.tsx" path="apps/web/app/[locale]/(platform)/app/products/[id]/edit/page.tsx"></mcfile>
- **Cliente da Página**: <mcfile name="page.client.tsx" path="apps/web/app/[locale]/(platform)/app/products/[id]/edit/page.client.tsx"></mcfile>
- **Aba de Certificados**: <mcfile name="CertificatesTab.tsx" path="apps/web/app/[locale]/(platform)/app/products/components/tabs/CertificatesTab.tsx"></mcfile>

### Funcionalidades Já Implementadas
- ✅ Aba de certificados integrada ao sistema de abas da página de edição
- ✅ Schema de validação completo com Zod (`certificateSchema`)
- ✅ Formulário de configuração com três abas: Design, Conteúdo e Requisitos
- ✅ Upload de imagens (background, logo, assinatura)
- ✅ Configurações de template (cores, textos, elementos visuais)
- ✅ Critérios de conclusão (progresso mínimo, todas as aulas)
- ✅ Persistência de dados e integração com API

### Pontos de Integração para Emissão Manual
**Localização Recomendada**: Dentro da <mcsymbol name="CertificatesTab" filename="CertificatesTab.tsx" path="apps/web/app/[locale]/(platform)/app/products/components/tabs/CertificatesTab.tsx" startline="1" type="function"></mcsymbol>

**Componentes a Adicionar**:
- Seção "Gerenciar Certificados" com lista de alunos elegíveis
- Botão "Emitir Certificado" para cada aluno
- Modal de confirmação para emissão manual
- Histórico de certificados emitidos

## 2. Área do Aluno (LMS)

### Estado Atual
- **Página do Curso**: <mcfile name="page.tsx" path="apps/web/app/[locale]/(lms)/account/course/[courseId]/page.tsx"></mcfile>
- **Exibição Básica**: Mostra apenas se certificado está "Disponível" ou "Não disponível"
- **Métricas**: <mcfile name="CourseMetrics.tsx" path="apps/web/app/[locale]/(lms)/account/components/CourseMetrics.tsx"></mcfile> inclui `certificatesCount`

### Implementações Necessárias

#### A. Exibição do Certificado no Perfil
**Localização**: Seção dedicada na página do curso
**Componentes a Criar**:
- `CertificateSection` - Seção principal do certificado
- `CertificatePreview` - Preview do certificado
- `CertificateDownload` - Botão de download
- `CertificateStatus` - Status de elegibilidade

#### B. Processo de Geração Automática
**Trigger**: Conclusão do curso (quando `overallProgress >= minimumProgress`)
**Localização**: Hook ou procedure que monitora progresso

#### C. Botão "Emitir Certificado"
**Funcionalidades**:
- Verificar elegibilidade do aluno
- Gerar certificado em PDF
- Salvar registro no banco de dados
- Disponibilizar para download
- Enviar por email (opcional)

## 3. Requisitos Técnicos para Integração

### APIs e Procedures Necessárias

#### A. Backend (packages/api)
```typescript
// Procedures a serem criadas:
- certificates.generate.useMutation()
- certificates.issue.useMutation()
- certificates.download.useQuery()
- certificates.listByStudent.useQuery()
- certificates.listByProduct.useQuery()
- certificates.checkEligibility.useQuery()
```

#### B. Banco de Dados
**Modelo Existente**: Já existe suporte no schema Prisma
- `Product.certificateTemplate` (JSON)
- `Course.certificateEnabled` (Boolean)
- `CourseEnrollment.completedAt` (DateTime)

**Modelo a Criar**:
```prisma
model Certificate {
  id          String   @id @default(cuid())
  userId      String
  productId   String
  courseId    String?
  issuedAt    DateTime @default(now())
  issuedBy    String?  // Para emissão manual
  fileUrl     String
  status      CertificateStatus @default(ACTIVE)

  user        User     @relation(fields: [userId], references: [id])
  product     Product  @relation(fields: [productId], references: [id])
  course      Course?  @relation(fields: [courseId], references: [id])
}

enum CertificateStatus {
  ACTIVE
  REVOKED
  EXPIRED
}
```

### Integração com Bunny.net
- Upload de certificados gerados
- URLs assinadas para download seguro
- Armazenamento de templates de certificado

## 4. Dados Necessários para Compor o Certificado

### Dados do Aluno
- Nome completo (`user.name`)
- Email (`user.email`)
- Data de conclusão (`enrollment.completedAt`)

### Dados do Curso
- Título (`product.title`)
- Descrição (`product.description`)
- Carga horária (`totalDuration`)
- Instrutor (`product.creator.name`)

### Dados do Template
- Título personalizado (`certificateTemplate.title`)
- Subtítulo (`certificateTemplate.subtitle`)
- Texto personalizado (`certificateTemplate.customText`)
- Cores (`primaryColor`, `secondaryColor`)
- Imagens (background, logo, assinatura)
- Configurações de exibição (`showCompletionDate`, `showDuration`)

## 5. Fluxos de Usuário

### A. Emissão Automática
1. Aluno completa curso (progresso >= `minimumProgress`)
2. Sistema verifica critérios de conclusão
3. Certificado é gerado automaticamente
4. Registro é salvo no banco de dados
5. Aluno recebe notificação por email
6. Certificado fica disponível na área do aluno

### B. Emissão Manual
1. Professor/Admin acessa aba de certificados
2. Visualiza lista de alunos elegíveis
3. Seleciona aluno e clica "Emitir Certificado"
4. Sistema gera certificado
5. Certificado é enviado ao aluno
6. Registro é salvo com flag de emissão manual

### C. Download pelo Aluno
1. Aluno acessa página do curso
2. Visualiza seção de certificado
3. Clica em "Baixar Certificado"
4. Sistema gera URL assinada
5. Download é iniciado
6. Acesso é registrado para auditoria

## 6. Validações e Regras de Negócio

### Critérios de Elegibilidade
- ✅ Curso deve ter `certificateEnabled = true`
- ✅ Aluno deve estar matriculado (`CourseEnrollment` ativo)
- ✅ Progresso deve ser >= `minimumProgress`
- ✅ Se `requireAllLessons = true`, todas as aulas devem estar completas
- ✅ Matrícula deve estar marcada como concluída (`completedAt` não nulo)

### Regras de Emissão
- 🔄 Um certificado por aluno por curso
- 🔄 Certificados podem ser reemitidos (nova versão)
- 🔄 Emissão manual sobrescreve automática
- 🔄 Histórico de emissões deve ser mantido

### Segurança
- 🔐 URLs de download com expiração (24h)
- 🔐 Verificação de propriedade (aluno só baixa seus certificados)
- 🔐 Logs de acesso e download
- 🔐 Validação de integridade do certificado

## 7. Próximos Passos Recomendados

1. **Criar modelo Certificate no Prisma**
2. **Implementar procedures de backend**
3. **Desenvolver gerador de PDF**
4. **Criar componentes da área do aluno**
5. **Implementar seção de gerenciamento na aba de certificados**
6. **Configurar integração com Bunny.net**
7. **Implementar sistema de notificações**
8. **Criar testes automatizados**

A estrutura atual já fornece uma base sólida para a implementação completa da funcionalidade de certificados, com a maior parte da configuração e validação já implementada na <mcfile name="CertificatesTab.tsx" path="apps/web/app/[locale]/(platform)/app/products/components/tabs/CertificatesTab.tsx"></mcfile>.
