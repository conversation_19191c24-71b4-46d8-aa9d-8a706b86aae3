'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import { Input } from '@ui/components/input';
import { Badge } from '@ui/components/badge';
import {
	CreditCard,
	Calendar,
	Download,
	CheckCircle,
	Clock,
	XCircle,
	Search,
	FileText,
	GraduationCap,
	Users,
	ChevronLeft,
	ChevronRight
} from 'lucide-react';
import { apiClient } from '@shared/lib/api-client';

const ITEMS_PER_PAGE = 10;

export default function BillingPage() {
	const [searchTerm, setSearchTerm] = useState('');
	const [currentPage, setCurrentPage] = useState(1);

	// Buscar ordens do usuário usando tRPC
	const { data: userOrders, isLoading } = apiClient.orders.getUserOrders.useQuery();



	// Filtrar e paginar ordens
	const { filteredOrders, paginatedOrders, totalPages, totalOrders } = useMemo(() => {
		if (!userOrders) return { filteredOrders: [], paginatedOrders: [], totalPages: 0, totalOrders: 0 };

		const filtered = userOrders.filter((order: any) =>
			order.product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
			order.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
			order.product.type.toLowerCase().includes(searchTerm.toLowerCase())
		);

		const totalPages = Math.ceil(filtered.length / ITEMS_PER_PAGE);
		const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
		const endIndex = startIndex + ITEMS_PER_PAGE;
		const paginated = filtered.slice(startIndex, endIndex);

		return {
			filteredOrders: filtered,
			paginatedOrders: paginated,
			totalPages,
			totalOrders: filtered.length
		};
	}, [userOrders, searchTerm, currentPage]);

	const getStatusBadge = (status: string) => {
		switch (status) {
					case 'PAID':
			return (
				<Badge className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200'>
					<CheckCircle className='h-3 w-3 mr-1' />
					Pago
				</Badge>
			);
		case 'PENDING':
			return (
				<Badge className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200'>
					<Clock className='h-3 w-3 mr-1' />
					Pendente
				</Badge>
			);
		case 'CANCELLED':
			return (
				<Badge className='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200'>
					<XCircle className='h-3 w-3 mr-1' />
					Cancelado
				</Badge>
			);
			default:
				return (
					<Badge className='bg-gray-100 text-gray-800'>
						{status}
					</Badge>
				);
		}
	};

	const getProductIcon = (type: string) => {
		switch (type) {
			case 'COURSE':
				return <GraduationCap className='h-5 w-5 text-blue-600' />;
			case 'EBOOK':
				return <FileText className='h-5 w-5 text-green-600' />;
			case 'MENTORING':
				return <Users className='h-5 w-5 text-purple-600' />;
			default:
				return <CreditCard className='h-5 w-5 text-gray-600' />;
		}
	};

	const getProductTypeLabel = (type: string) => {
		switch (type) {
			case 'COURSE':
				return 'Curso';
			case 'EBOOK':
				return 'E-book';
			case 'MENTORING':
				return 'Mentoria';
			default:
				return 'Produto';
		}
	};

	const formatCurrency = (value: number | null | undefined) => {
		// Handle null, undefined, or NaN values
		if (value === null || value === undefined || isNaN(value)) {
			return new Intl.NumberFormat('pt-BR', {
				style: 'currency',
				currency: 'BRL'
			}).format(0);
		}

		// Se o valor já está em reais (número normal), usar diretamente
		// Se está em centavos (muito grande), dividir por 100
		const realValue = value > 1000 ? value / 100 : value;

		return new Intl.NumberFormat('pt-BR', {
			style: 'currency',
			currency: 'BRL'
		}).format(realValue);
	};

	if (isLoading) {
		return (
			<div className='flex items-center justify-center min-h-screen'>
				<div className='animate-spin border-b-2 border-primary h-8 rounded-full w-8' />
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50/30'>
			<div className='container mx-auto px-4 py-6 md:py-8'>
				<div className='space-y-6'>
					{/* Header */}
					<div className='bg-gradient-to-r from-blue-50 via-blue-50/50 to-transparent rounded-2xl p-6 md:p-8'>
						<div className='flex items-center gap-4'>
							<div className='p-3 bg-blue-100 rounded-xl'>
								<CreditCard className='h-6 w-6 text-blue-600' />
							</div>
							<div>
								<h1 className='text-2xl md:text-3xl font-bold text-gray-900'>Histórico de Compras</h1>
								<p className='text-gray-600 mt-1'>Visualize todas as suas compras e pagamentos</p>
							</div>
						</div>
					</div>

					{/* Search Bar */}
					{userOrders && userOrders.length > 0 && (
						<div className='relative'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
							<Input
								type='text'
								placeholder='Buscar compras...'
								value={searchTerm}
								onChange={(e) => {
									setSearchTerm(e.target.value);
									setCurrentPage(1); // Reset to first page when searching
								}}
								className='pl-10 bg-white border-gray-200 focus:border-primary'
							/>
						</div>
					)}

					{/* Summary Cards */}
					{userOrders && userOrders.length > 0 && (
						<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
							<Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
								<CardContent className='p-6'>
									<div className='flex items-center gap-3'>
										<div className="p-2 bg-green-100 rounded-lg">
											<CheckCircle className='h-5 w-5 text-green-600' />
										</div>
										<div>
											<p className='text-sm text-gray-600 font-medium'>Compras Pagas</p>
											<p className='text-2xl font-bold text-gray-900'>
												{userOrders.filter((order: any) => order.status === 'PAID').length}
											</p>
										</div>
									</div>
								</CardContent>
							</Card>
							<Card className="bg-white shadow-sm hover:shadow-md transition-shadow">
								<CardContent className='p-6'>
									<div className='flex items-center gap-3'>
										<div className="p-2 bg-yellow-100 rounded-lg">
											<Clock className='h-5 w-5 text-yellow-600' />
										</div>
										<div>
											<p className='text-sm text-gray-600 font-medium'>Pendentes</p>
											<p className='text-2xl font-bold text-gray-900'>
												{userOrders.filter((order: any) => order.status === 'PENDING').length}
											</p>
										</div>
									</div>
								</CardContent>
							</Card>

						</div>
					)}

					{/* Orders List */}
					{paginatedOrders.length > 0 ? (
						<>
							<div className='flex justify-between items-center'>
								<p className='text-sm text-gray-600'>
									Mostrando {((currentPage - 1) * ITEMS_PER_PAGE) + 1} a {Math.min(currentPage * ITEMS_PER_PAGE, totalOrders)} de {totalOrders} compras
								</p>
							</div>
							<div className='space-y-4'>
								{paginatedOrders.map((order: any) => (
								<Card key={order.id} className='hover:shadow-lg transition-all duration-300 bg-white border border-gray-100'>
									<CardContent className='p-6'>
										<div className='flex items-center justify-between'>
											<div className='flex items-start gap-4'>
												<div className='p-3 bg-gray-50 rounded-xl'>
													{getProductIcon(order.product.type)}
												</div>
												<div className='space-y-3'>
													<div className='flex items-center gap-3 flex-wrap'>
														<h3 className='font-semibold text-gray-900 text-lg'>
															{order.product.title}
														</h3>
														{getStatusBadge(order.status)}
														<Badge className='border border-gray-300 bg-white text-gray-700 text-xs'>
															{getProductTypeLabel(order.product.type)}
														</Badge>
													</div>
													<div className='flex items-center gap-4 text-sm text-gray-500 flex-wrap'>
														<div className='flex items-center gap-1'>
															<Calendar className='h-3 w-3' />
															<span>{new Date(order.createdAt).toLocaleDateString('pt-BR')}</span>
														</div>
														{order.paymentMethod && (
															<>
																<span>•</span>
																<span>{order.paymentMethod}</span>
															</>
														)}
														<span>•</span>
														<span className='text-xs text-gray-400'>#{order.id.slice(-8)}</span>
													</div>
													<div className='text-xl font-bold text-primary'>
														{formatCurrency(order.amount || 0)}
													</div>
												</div>
											</div>
											<div className='flex flex-col gap-2'>
												{order.status === 'PAID' && (
													<Button variant='outline' size='sm' className="bg-white hover:bg-gray-50">
														<Download className='h-4 w-4 mr-2' />
														Recibo
													</Button>
												)}
											</div>
										</div>
									</CardContent>
								</Card>
								))}
							</div>

							{/* Pagination */}
							{totalPages > 1 && (
								<div className='flex items-center justify-between'>
									<div className='flex items-center gap-2'>
										<Button
											variant='outline'
											size='sm'
											onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
											disabled={currentPage === 1}
											className='border border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
										>
											<ChevronLeft className='h-4 w-4 mr-1' />
											Anterior
										</Button>
										<Button
											variant='outline'
											size='sm'
											onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
											disabled={currentPage === totalPages}
											className='border border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
										>
											Próxima
											<ChevronRight className='h-4 w-4 ml-1' />
										</Button>
									</div>
									<div className='text-sm text-gray-600'>
										Página {currentPage} de {totalPages}
									</div>
								</div>
							)}
						</>
					) : filteredOrders.length === 0 && userOrders && userOrders.length > 0 ? (
						/* No results for search */
						<Card className="bg-white shadow-sm">
							<CardContent className='py-12 text-center'>
								<div className='space-y-4'>
									<div className='mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center'>
										<Search className='h-8 w-8 text-gray-400' />
									</div>
									<div>
										<h3 className='text-lg font-medium text-gray-900'>
											Nenhuma compra encontrada
										</h3>
										<p className='text-gray-600 mt-2'>
											Tente buscar com outros termos
										</p>
									</div>
									<Button onClick={() => setSearchTerm('')} className='border border-gray-300 bg-white text-gray-700 hover:bg-gray-50'>
										Limpar Busca
									</Button>
								</div>
							</CardContent>
						</Card>
					) : (
						/* Empty State */
						<Card className="bg-white shadow-sm">
							<CardContent className='py-12 text-center'>
								<div className='space-y-4'>
									<div className='mx-auto w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center'>
										<CreditCard className='h-10 w-10 text-primary' />
									</div>
									<div>
										<h3 className='text-xl font-semibold text-gray-900'>
											Nenhuma compra ainda
										</h3>
										<p className='text-gray-600 mt-2'>
											Quando você fizer uma compra, o histórico aparecerá aqui
										</p>
									</div>
									<Button asChild size="lg" className="bg-primary hover:bg-primary/90">
										<a href='/products'>
											Explorar Produtos
										</a>
									</Button>
								</div>
							</CardContent>
						</Card>
					)}
				</div>
			</div>
		</div>
	);
}
