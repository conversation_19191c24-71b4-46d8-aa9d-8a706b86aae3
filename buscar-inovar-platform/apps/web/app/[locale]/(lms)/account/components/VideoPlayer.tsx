// apps/web/app/[locale]/(lms)/account/components/VideoPlayer.tsx
'use client';

import { useState, useEffect, useRef } from 'react';

interface VideoPlayerProps {
	url: string;
	onComplete?: () => void;
	autoPlay?: boolean;
}

export function VideoPlayer({
	url,
	onComplete,
	autoPlay = false,
}: VideoPlayerProps) {
	const iframeRef = useRef<HTMLIFrameElement>(null);
	const [isBunnyUrl, setIsBunnyUrl] = useState(false);

	// Verificar se a URL é do Bunny.net
	useEffect(() => {
		console.log('[VideoPlayer] Received URL:', url);
		
		// Verificar diferentes formatos de URL do Bunny:
		// 1. URLs completas do CDN: https://buscarinovar.b-cdn.net/products/xxx.mp4
		// 2. URLs do iframe: https://iframe.mediadelivery.net/embed/...
		// 3. IDs de vídeo: cb9c36e2-0bc8-4053-840d-2c2f3ea35647
		const isBunny = url.includes('bunny.net') ||
						url.includes('b-cdn.net') ||
						url.includes('mediadelivery.net') ||
						// Se for apenas um ID (UUID ou string alfanumérica), assumir que é do Bunny
						(/^[a-zA-Z0-9-_]{8,}$/.test(url) && !url.includes('/') && !url.includes('http'));
		
		setIsBunnyUrl(isBunny);
		console.log('[VideoPlayer] URL analysis:', { 
			url, 
			isBunny, 
			urlLength: url.length,
			isFullUrl: url.includes('http'),
			isCdnUrl: url.includes('b-cdn.net'),
			isVideoId: /^[a-zA-Z0-9-_]{8,}$/.test(url) && !url.includes('/') && !url.includes('http')
		});
	}, [url]);

	// Ouvir mensagens do iframe para detectar quando o vídeo termina
	useEffect(() => {
		const handleMessage = (event: MessageEvent) => {
			if (typeof event.data === 'string') {
				const [action] = event.data.split(',');

				if (action === 'finish') {
					onComplete?.();
				}
			}
		};

		window.addEventListener('message', handleMessage);
		return () => window.removeEventListener('message', handleMessage);
	}, [onComplete]);

	// Garantir que todos os vídeos usem o player do Bunny.net
	const getEmbedUrl = () => {
		if (!isBunnyUrl) {
			console.warn('[VideoPlayer] URL não é do Bunny.net, usando player HTML padrão:', url);
			return url;
		}

		// Se já é uma URL completa do iframe do Bunny, usar diretamente
		if (url.includes('iframe.mediadelivery.net/embed/')) {
			const separator = url.includes('?') ? '&' : '?';
			const finalUrl = `${url}${separator}autoplay=${autoPlay ? 'true' : 'false'}&preload=true`;
			console.log('[VideoPlayer] Using existing iframe URL:', finalUrl);
			return finalUrl;
		}

		const libraryId = process.env.NEXT_PUBLIC_BUNNY_LIBRARY_ID || '383178';
		let videoId = url;

		// Se é uma URL completa do CDN, extrair o ID do arquivo
		if (url.includes('http') && url.includes('b-cdn.net')) {
			console.log('[VideoPlayer] Converting CDN URL to Bunny player:', url);
			// Extrair o nome do arquivo da URL do CDN
			const urlParts = url.split('/');
			videoId = urlParts[urlParts.length - 1];
			// Remover extensão do arquivo
			videoId = videoId.replace(/\.(mp4|webm|mov|avi)$/i, '');
		} else if (url.includes('/')) {
			// Extrair ID do vídeo se a URL contém outros elementos
			const parts = url.split('/');
			videoId = parts[parts.length - 1];
			videoId = videoId.replace(/\.(mp4|webm|mov|avi)$/i, '');
		}

		// Construir URL do iframe do Bunny.net para todos os casos
		const embedUrl = `https://iframe.mediadelivery.net/embed/${libraryId}/${videoId}?autoplay=${autoPlay ? 'true' : 'false'}&preload=true`;
		console.log('[VideoPlayer] Using Bunny iframe player for:', videoId, '-> URL:', embedUrl);
		return embedUrl;
	};

	const embedUrl = getEmbedUrl();

	return (
		<div className='relative aspect-video bg-black rounded-lg overflow-hidden'>
			{isBunnyUrl ? (
				// Player iframe do Bunny.net para todos os vídeos do Bunny
				<iframe
					ref={iframeRef}
					src={embedUrl}
					className='absolute inset-0 w-full h-full'
					allow='accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture'
					allowFullScreen
					title='Aula'
					style={{ border: 'none' }}
				/>
			) : (
				// Player HTML padrão apenas para URLs externas (não Bunny.net)
				<video
					controls
					autoPlay={autoPlay}
					className='absolute inset-0 w-full h-full'
					onEnded={() => onComplete?.()}
					src={embedUrl}
				>
					<source src={embedUrl} type='video/mp4' />
					Seu navegador não suporta o elemento de vídeo.
				</video>
			)}
		</div>
	);
}
