'use client';

import { But<PERSON> } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import {
	BookOpen,
	Download,
	ExternalLink,
	PlayCircle,
	Calendar,
	Clock,
	FileText,
	GraduationCap,
	Users,
	CheckCircle2,
	ArrowRight,
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { apiClient } from '@shared/lib/api-client';
import { toast } from 'sonner';

interface ProductAccessCardProps {
	product: {
		id: string;
		title: string;
		type: 'COURSE' | 'EBOOK' | 'MENTORING';
		thumbnail?: string | null;
		description?: string | null;
		course?: {
			id: string;
			totalLessons?: number;
		} | null;
		ebook?: {
			id: string;
			fileUrl?: string | null;
			fileUrls?: any;
			format?: string[];
		} | null;
		mentoring?: {
			id: string;
			duration?: number;
			schedulingUrl?: string;
		} | null;
	};
	enrollment?: {
		progress?: any;
		lastAccessedAt?: string;
	} | null;
	purchaseDate?: string;
}

export function ProductAccessCard({ product, enrollment, purchaseDate }: ProductAccessCardProps) {
	const [isDownloading, setIsDownloading] = useState(false);

	// Download mutation
	const downloadEbook = apiClient.ebooks.download.useMutation({
		onSuccess: (result) => {
			if (result.downloadUrl) {
				// Abrir a URL protegida em uma nova aba
				window.open(result.downloadUrl, '_blank');
				toast.success('🔒 Download protegido iniciado! O ebook foi protegido com suas informações.');
			}
			setIsDownloading(false);
		},
		onError: (error) => {
			toast.error('Erro ao baixar o ebook protegido');
			console.error('Protected download error:', error);
			setIsDownloading(false);
		}
	});

	const getProductIcon = () => {
		switch (product.type) {
			case 'COURSE':
				return <GraduationCap className='h-5 w-5' />;
			case 'MENTORING':
				return <Users className='h-5 w-5' />;
			case 'EBOOK':
				return <FileText className='h-5 w-5' />;
			default:
				return <BookOpen className='h-5 w-5' />;
		}
	};

	const getProductTypeLabel = () => {
		switch (product.type) {
			case 'COURSE':
				return 'Curso';
			case 'MENTORING':
				return 'Mentoria';
			case 'EBOOK':
				return 'E-book';
			default:
				return 'Produto';
		}
	};

	const getProductTypeColor = () => {
		switch (product.type) {
			case 'COURSE':
				return 'bg-blue-100 text-blue-800 border-blue-200';
			case 'MENTORING':
				return 'bg-purple-100 text-purple-800 border-purple-200';
			case 'EBOOK':
				return 'bg-green-100 text-green-800 border-green-200';
			default:
				return 'bg-gray-100 text-gray-800 border-gray-200';
		}
	};

	const getProgressPercentage = () => {
		if (!enrollment?.progress || product.type !== 'COURSE') return 0;
		// Progress agora é um número simples
		return enrollment.progress || 0;
	};

	const handleEbookDownload = async (format: string, url: string, e: React.MouseEvent) => {
		e.preventDefault();
		e.stopPropagation();

		setIsDownloading(true);
		try {
			// Usar o sistema de download protegido
			await downloadEbook.mutateAsync({
				productId: product.id,
				format: format,
				includeWatermark: true,
				includeSignature: true,
				securityLevel: 'STANDARD'
			});
		} catch (error) {
			console.error('Protected download error:', error);
		}
	};

	const getMainHref = () => {
		switch (product.type) {
			case 'COURSE':
				return `/account/course/${product.course?.id}`;
			case 'MENTORING':
				return product.mentoring?.schedulingUrl || `/account/mentoring/${product.mentoring?.id}`;
			case 'EBOOK':
				return `/account/downloads`; // Redirect to downloads page for ebooks
			default:
				return '#';
		}
	};

	const progress = getProgressPercentage();

	return (
		<Link href={getMainHref()} className="group block">
			<Card className='overflow-hidden hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:scale-[1.02] bg-white'>
				<div className='relative'>
					{product.thumbnail ? (
						<div
							className='h-36 md:h-48 bg-cover bg-center relative'
							style={{ backgroundImage: `url(${product.thumbnail})` }}
						>
							<div className="absolute inset-0 bg-black/10 group-hover:bg-black/5 transition-colors" />
						</div>
					) : (
						<div className='h-36 md:h-48 bg-gradient-to-br from-primary/20 via-primary/10 to-primary/5 flex items-center justify-center relative'>
							<div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-transparent" />
							<div className="relative z-10 p-4 text-primary">
								{getProductIcon()}
							</div>
						</div>
					)}

					{/* Type Badge */}
					<div className='absolute top-3 left-3'>
						<Badge className={`${getProductTypeColor()} text-xs font-medium border bg-white`}>
							{getProductTypeLabel()}
						</Badge>
					</div>


				</div>

				<CardHeader className='pb-3 p-4 md:p-6'>
					<CardTitle className='text-base md:text-lg line-clamp-2 leading-tight group-hover:text-primary transition-colors'>
						{product.title}
					</CardTitle>
					{product.description && (
						<p className='text-sm text-muted-foreground line-clamp-2 mt-2'>
							{product.description}
						</p>
					)}
				</CardHeader>

				<CardContent className='space-y-4 p-4 md:p-6 pt-0'>
					{/* Purchase date */}
					{purchaseDate && (
						<p className='text-xs text-muted-foreground flex items-center gap-1'>
							<Calendar className="h-3 w-3" />
							Adquirido em {new Date(purchaseDate).toLocaleDateString('pt-BR')}
						</p>
					)}



					{/* Action Buttons - Only for specific actions that need to prevent navigation */}
					{product.type === 'EBOOK' && (
						<div className="space-y-2">
							{(() => {
								const fileUrls = product.ebook?.fileUrls || {};
								const hasFiles = Object.keys(fileUrls).length > 0;

								if (!hasFiles && !product.ebook?.fileUrl) {
									return (
										<div className='text-center py-2'>
											<p className='text-sm text-gray-500'>Arquivo não disponível</p>
										</div>
									);
								}

								return hasFiles ? (
									<div className="flex flex-wrap gap-2">
										{Object.entries(fileUrls).slice(0, 2).map(([format, url]) => (
											<Button
												key={format}
												variant='outline'
												size="sm"
												className='flex-1 text-xs'
												onClick={(e) => handleEbookDownload(format, url as string, e)}
												disabled={isDownloading}
											>
												<Download className='h-3 w-3 mr-1' />
												{format.toUpperCase()}
											</Button>
										))}
									</div>
								) : (
									<Button
										variant='outline'
										size="sm"
										className='w-full text-xs'
										onClick={(e) => handleEbookDownload('pdf', product.ebook?.fileUrl || '', e)}
										disabled={isDownloading}
									>
										<Download className='h-3 w-3 mr-2' />
										Baixar E-book
									</Button>
								);
							})()}
						</div>
					)}

					{/* Mentoring Duration */}
					{product.type === 'MENTORING' && product.mentoring?.duration && (
						<div className='flex items-center justify-center text-sm text-muted-foreground'>
							<Clock className='h-4 w-4 mr-1' />
							{product.mentoring.duration} minutos
						</div>
					)}

					{/* Hover Arrow */}
					<div className="flex items-center justify-between pt-2">
						<div className="flex items-center gap-2 text-sm text-gray-500">
							{product.type === 'COURSE' && <PlayCircle className="h-4 w-4" />}
							{product.type === 'EBOOK' && <FileText className="h-4 w-4" />}
							{product.type === 'MENTORING' && <Users className="h-4 w-4" />}
							<span>
								{product.type === 'COURSE' && 'Acessar Curso'}
								{product.type === 'EBOOK' && 'Ver Downloads'}
								{product.type === 'MENTORING' && 'Agendar'}
							</span>
						</div>
						<ArrowRight className="h-4 w-4 text-primary group-hover:translate-x-1 transition-transform" />
					</div>
				</CardContent>
			</Card>
		</Link>
	);
}
