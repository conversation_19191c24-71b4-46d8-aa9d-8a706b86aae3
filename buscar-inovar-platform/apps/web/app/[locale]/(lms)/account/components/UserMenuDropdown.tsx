'use client';

import { Link } from '@i18n/routing';
import { useUser } from '@saas/auth/hooks/use-user';
import { UserAvatar } from '@shared/components/UserAvatar';
import { apiClient } from '@shared/lib/api-client';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@ui/components/dropdown-menu';
import { useToast } from '@ui/hooks/use-toast';
import type { ApiOutput } from 'api';
import {
	UserIcon,
	SettingsIcon,
	LogOutIcon,
	BookOpen,
	Trophy,
	FileText,
	CreditCard,
	Bell,
	HelpCircle,
	ChevronDown,
} from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import { useState } from 'react';

// Define the User type from the API output
type User = NonNullable<ApiOutput['auth']['user']>;

export function UserMenuDropDown({ user }: { user: User }) {
	const currentLocale = useLocale();
	const t = useTranslations();
	const { logout } = useUser();
	const { toast } = useToast();

	if (!user) {
		return null;
	}

	const { name, email, avatarUrl } = user;

	return (
		<DropdownMenu modal={false}>
			<DropdownMenuTrigger asChild>
				<div className='flex items-center gap-2 cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors'>
					<UserAvatar name={name ?? ''} avatarUrl={avatarUrl} />
					<div className='hidden sm:block text-left'>
						<div className='text-sm font-medium text-gray-900 truncate max-w-32'>
							{name}
						</div>
						<div className='text-xs text-gray-500 truncate max-w-32'>
							Aluno
						</div>
					</div>
					<ChevronDown className='h-3 w-3 text-gray-400 hidden sm:block' />
				</div>
			</DropdownMenuTrigger>

			<DropdownMenuContent align='end' className='w-64'>
				<DropdownMenuLabel>
					<div className='flex items-center gap-3'>
						<UserAvatar name={name ?? ''} avatarUrl={avatarUrl} />
						<div>
							<div className='font-medium'>{name}</div>
							<div className='text-xs text-gray-500 font-normal'>{email}</div>
						</div>
					</div>
				</DropdownMenuLabel>

				<DropdownMenuSeparator />

				{/* Learning Section */}
				<DropdownMenuItem asChild>
					<Link href='/account'>
						<BookOpen className='mr-2 size-4' />
						Meus Conteúdos
					</Link>
				</DropdownMenuItem>

				<DropdownMenuItem asChild>
					<Link href='/account/downloads'>
						<FileText className='mr-2 size-4' />
						Downloads
					</Link>
				</DropdownMenuItem>

				<DropdownMenuSeparator />

				{/* Account Section */}
				<DropdownMenuItem asChild>
					<Link href='/account/profile'>
						<UserIcon className='mr-2 size-4' />
						Meu Perfil
					</Link>
				</DropdownMenuItem>

				<DropdownMenuItem asChild>
					<Link href='/account/billing'>
						<CreditCard className='mr-2 size-4' />
						Histórico de Compras
					</Link>
				</DropdownMenuItem>

				<DropdownMenuSeparator />

				{/* Support Section */}
				<DropdownMenuItem asChild>
					<Link href='/support'>
						<HelpCircle className='mr-2 size-4' />
						Central de Ajuda
					</Link>
				</DropdownMenuItem>

				<DropdownMenuSeparator />

				<DropdownMenuItem onClick={logout} className='text-red-600 focus:text-red-600'>
					<LogOutIcon className='mr-2 size-4' />
					Sair da Conta
				</DropdownMenuItem>
			</DropdownMenuContent>
		</DropdownMenu>
	);
}
