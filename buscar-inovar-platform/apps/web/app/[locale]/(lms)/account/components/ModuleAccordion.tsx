// apps/web/app/(lms)/account/classroom/[courseId]/components/ModuleAccordion.tsx
import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from '@ui/components/accordion';

import { LessonCard } from './LessonCard';
import { CheckCircle, PlayCircle } from 'lucide-react';
import { cn } from '@ui/lib';

interface ModuleAccordionProps {
	module: any;
	currentModuleOrder?: number;
	currentLessonOrder?: number;
	onLessonSelect: (moduleOrder: number, lessonOrder: number) => void;
	expanded: boolean;
}

export function ModuleAccordion({
	module,
	currentModuleOrder,
	currentLessonOrder,
	onLessonSelect,
	expanded,
}: ModuleAccordionProps) {
	// Calcular o progresso do módulo
	const completedLessons = module.lessons.filter(
		(lesson: any) => lesson.completed
	).length;
	const totalLessons = module.lessons.length;
	const progress =
		totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0;
	const isCompleted = progress === 100;
	const isCurrentModule = module.order === currentModuleOrder;

	return (
		<Accordion
			type='single'
			defaultValue={expanded ? String(module.order) : undefined}
			className='mb-3 border rounded-lg overflow-hidden bg-white shadow-sm'
		>
			<AccordionItem value={String(module.order)} className='border-none'>
				<AccordionTrigger className={cn(
					'hover:no-underline px-3 md:px-4 py-3 md:py-4 transition-colors',
					isCurrentModule && 'bg-primary/5'
				)}>
					<div className='flex items-center gap-3 w-full'>
						<div
							className={cn(
								'flex h-8 w-8 md:h-10 md:w-10 items-center justify-center rounded-full flex-shrink-0',
								isCompleted ? 'bg-emerald-100 text-emerald-600' :
								isCurrentModule ? 'bg-primary/20 text-primary' : 'bg-gray-100 text-gray-600'
							)}
						>
							{isCompleted ? (
								<CheckCircle className='h-4 w-4 md:h-5 md:w-5' />
							) : (
								<span className='text-sm md:text-base font-semibold'>{module.order + 1}</span>
							)}
						</div>
						<div className='text-left flex-1 min-w-0'>
							<p className={cn(
								'font-medium text-sm md:text-base truncate',
								isCurrentModule && 'text-primary'
							)}>
								{module.title}
							</p>
							<div className='flex items-center gap-2 text-xs md:text-sm text-gray-500 mt-1'>
								<span>
									{completedLessons}/{totalLessons} {totalLessons === 1 ? 'aula' : 'aulas'}
								</span>
								<span>•</span>
								<span>{progress}% concluído</span>
							</div>
						</div>

						{/* Progress bar for mobile */}
						<div className='md:hidden w-12 flex-shrink-0'>
							<div className='h-1.5 bg-gray-200 rounded-full overflow-hidden'>
								<div
									className={cn(
										'h-full transition-all duration-300',
										isCompleted ? 'bg-emerald-500' : 'bg-primary'
									)}
									style={{ width: `${progress}%` }}
								/>
							</div>
						</div>
					</div>
				</AccordionTrigger>
				<AccordionContent>
					<div className='space-y-1 px-3 md:px-4 pb-3 md:pb-4'>
						{module.lessons.map((lesson: any) => (
							<LessonCard
								key={lesson.order}
								lesson={lesson}
								isActive={
									module.order === currentModuleOrder &&
									lesson.order === currentLessonOrder
								}
								onClick={() => onLessonSelect(module.order, lesson.order)}
							/>
						))}
					</div>
				</AccordionContent>
			</AccordionItem>
		</Accordion>
	);
}
