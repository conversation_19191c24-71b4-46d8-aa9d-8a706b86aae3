// apps/web/app/(lms)/account/components/WelcomeSection.tsx
'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent } from '@ui/components/card';
import { Button } from '@ui/components/button';
import {
	CheckCircleIcon,
	ArrowRightIcon,
	BookOpenIcon,
	DownloadIcon,
	CalendarIcon,
	XIcon,
	Sparkles
} from 'lucide-react';
import { Link } from '@i18n/routing';

interface WelcomeSectionProps {
	user?: any;
	recentPurchases?: any[];
}

export function WelcomeSection({ user, recentPurchases = [] }: WelcomeSectionProps) {
	const searchParams = useSearchParams();
	const [showWelcome, setShowWelcome] = useState(false);
	const [welcomeData, setWelcomeData] = useState<any>(null);
	const [userName, setUserName] = useState('');
	const [greeting, setGreeting] = useState('');

	// Set greeting and fetch user data
	useEffect(() => {
		const hour = new Date().getHours();
		if (hour < 12) setGreeting('Bom dia');
		else if (hour < 18) setGreeting('Boa tarde');
		else setGreeting('Boa noite');

		if (user?.name) {
			setUserName(user.name.split(' ')[0]);
		}
	}, [user]);

	// Check for purchase success or new user indicators
	useEffect(() => {
		const fromPurchase = searchParams.get('from') === 'purchase';
		const newPurchase = searchParams.get('new_purchase');
		const productId = searchParams.get('product_id');

		// Check if user has recent purchases (within last 24 hours)
		const hasRecentPurchase = recentPurchases.some(purchase => {
			const purchaseDate = new Date(purchase.purchaseDate);
			const now = new Date();
			const hoursDiff = (now.getTime() - purchaseDate.getTime()) / (1000 * 60 * 60);
			return hoursDiff <= 24;
		});

		if (fromPurchase || newPurchase || hasRecentPurchase) {
			setShowWelcome(true);

			// Find the specific product if available
			let product = null;
			if (productId) {
				product = recentPurchases.find(p => p.id === productId);
			} else if (hasRecentPurchase) {
				// Get the most recent purchase
				product = recentPurchases.sort((a, b) =>
					new Date(b.purchaseDate).getTime() - new Date(a.purchaseDate).getTime()
				)[0];
			}

			setWelcomeData(product);
		}
	}, [searchParams, recentPurchases]);

	const getProductIcon = (type: string) => {
		switch (type) {
			case 'COURSE': return <BookOpenIcon className="h-4 w-4" />;
			case 'EBOOK': return <DownloadIcon className="h-4 w-4" />;
			case 'MENTORING': return <CalendarIcon className="h-4 w-4" />;
			default: return <Sparkles className="h-4 w-4" />;
		}
	};

	const getProductTypeText = (type: string) => {
		switch (type) {
			case 'COURSE': return 'curso';
			case 'EBOOK': return 'e-book';
			case 'MENTORING': return 'mentoria';
			default: return 'produto';
		}
	};

	const getAccessUrl = (product: any) => {
		switch (product.type) {
			case 'COURSE':
				return `/account/course/${product.course?.id || product.id}`;
			case 'EBOOK':
				return '/account';
			case 'MENTORING':
				return product.mentoring?.schedulingUrl || '/account';
			default:
				return '/account';
		}
	};

	// Render purchase welcome if applicable
	if (showWelcome && welcomeData) {
		return (
			<Card className="mb-4 border-green-200 bg-green-50">
				<CardContent className="p-4">
					<div className="flex items-start justify-between mb-3">
						<div className="flex items-center gap-2">
							<CheckCircleIcon className="h-5 w-5 text-green-600 flex-shrink-0" />
							<div>
								<h3 className="font-semibold text-green-800 text-sm">
									🎉 Parabéns, {userName}!
								</h3>
								<p className="text-green-700 text-xs">
									Seu {getProductTypeText(welcomeData.type)} está pronto
								</p>
							</div>
						</div>
						<Button
							variant="ghost"
							size="sm"
							className="h-6 w-6 p-0 text-green-600 hover:bg-green-100"
							onClick={() => setShowWelcome(false)}
						>
							<XIcon className="h-4 w-4" />
						</Button>
					</div>

					<div className="flex items-center gap-3">
						<div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
							{getProductIcon(welcomeData.type)}
						</div>
						<div className="flex-1 min-w-0">
							<h4 className="font-medium text-gray-900 text-sm truncate">
								{welcomeData.title}
							</h4>
							<Button asChild size="sm" className="mt-2 h-8 text-xs bg-green-600 hover:bg-green-700">
								<Link href={getAccessUrl(welcomeData)}>
									Começar
									<ArrowRightIcon className="ml-1 h-3 w-3" />
								</Link>
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		);
	}

	// Default welcome section - mais simples
	return (
		<div className='mb-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100'>
			<div className='flex items-center gap-3'>
				<div className="flex-shrink-0 w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
					<Sparkles className="h-5 w-5 text-white" />
				</div>
				<div className="flex-1 min-w-0">
					<h2 className='font-semibold text-gray-900 text-lg leading-tight'>
						{greeting}, {userName || 'bem-vindo(a)'}! 👋
					</h2>
					<p className='text-gray-600 text-sm mt-1'>
						Continue aprendendo e alcance seus objetivos
					</p>
				</div>
			</div>
		</div>
	);
}
