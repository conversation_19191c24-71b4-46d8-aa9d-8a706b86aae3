// apps/web/app/(lms)/account/classroom/[courseId]/components/ResourcesList.tsx
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { FileText, Link2, Download } from 'lucide-react';
import { Button } from '@ui/components/button';

export function ResourcesList({ resources }) {
	const getIcon = (type) => {
		switch (type) {
			case 'pdf':
				return FileText;
			case 'link':
				return Link2;
			default:
				return Download;
		}
	};

	return (
		<Card>
			<CardHeader>
				<CardTitle>Materiais Complementares</CardTitle>
			</CardHeader>
			<CardContent>
				<div className='space-y-2'>
					{resources.map((resource) => {
						const Icon = getIcon(resource.type);
						return (
							<Button
								key={resource.id}
								variant='ghost'
								className='w-full justify-start'
								asChild
							>
								<a
									href={resource.url}
									target='_blank'
									rel='noopener noreferrer'
									className='flex items-center gap-2'
								>
									<Icon className='h-4 w-4' />
									<span>{resource.title}</span>
								</a>
							</Button>
						);
					})}
				</div>
			</CardContent>
		</Card>
	);
}
