// apps/web/app/[locale]/(lms)/account/classroom/[courseId]/page.tsx
'use client';

import { useEffect, useState, use } from 'react';
import { useCourseNavigation } from '@saas/courses/hooks/use-course-navigation';
import { useLessonCompletion } from '@saas/courses/hooks/use-lesson-completion';

import { Button } from '@ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import {
	ArrowLeft,
	ArrowRight,
	CheckCircle,
	ChevronLeft,
	ChevronRight,
	Download,
	ExternalLink,
	Loader2,
	User,
	Clock,
	BookOpen,
	Menu,
	X,
	Award,
	FileText,
	Link2,
	PlayCircle,
	MessageSquare,
	Send,
	PanelRightOpen,
	PanelRightClose,
} from 'lucide-react';
import Link from 'next/link';
import { VideoPlayer } from '../../components/VideoPlayer';
import { ModuleAccordion } from '../../components/ModuleAccordion';
import { Progress } from '@ui/components/progress';
import { cn } from '@ui/lib';
import { Separator } from '@ui/components/separator';
import { useRouter, useSearchParams } from 'next/navigation';
import { Skeleton } from '@ui/components/skeleton';
import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/avatar';
import { Textarea } from '@ui/components/textarea';
import { useToast } from '@ui/hooks/use-toast';


interface PageParams {
	params: Promise<{
		courseId: string;
	}>;
}

export default function ClassroomPage({ params }: PageParams) {
	const resolvedParams = use(params);
	const courseId = resolvedParams.courseId;
	const router = useRouter();
	const searchParams = useSearchParams();
	const [showSidebar, setShowSidebar] = useState(true); // Desktop sidebar aberta por padrão
	const [showMobileSidebar, setShowMobileSidebar] = useState(false); // Mobile sidebar fechada por padrão
	const [comment, setComment] = useState('');
	const [comments, setComments] = useState<Array<{
		id: string;
		text: string;
		author: string;
		createdAt: Date;
	}>>([]);
	const { toast } = useToast();

	// Obter o moduleOrder e lessonOrder da URL
	const moduleOrderFromUrl = searchParams.get('moduleOrder');
	const lessonOrderFromUrl = searchParams.get('lessonOrder');

	const {
		course,
		isLoading,
		currentModuleOrder,
		currentLessonOrder,
		currentLesson,
		navigateToLesson,
	} = useCourseNavigation(courseId);

	// Se não houver moduleOrder ou lessonOrder na URL, redirecionar para a primeira aula
	useEffect(() => {
		if (
			course &&
			(!moduleOrderFromUrl || !lessonOrderFromUrl) &&
			course.modules.length > 0 &&
			course.modules[0].lessons.length > 0
		) {
			const firstModule = course.modules[0];
			const firstLesson = firstModule.lessons[0];
			router.replace(
				`/account/classroom/${courseId}?moduleOrder=${firstModule.order}&lessonOrder=${firstLesson.order}`
			);
		}
	}, [course, moduleOrderFromUrl, lessonOrderFromUrl, courseId, router]);

	// Função para navegar para a próxima aula
	const navigateToNextLesson = () => {
		if (
			!course ||
			currentModuleOrder === undefined ||
			currentLessonOrder === undefined
		)
			return;

		const currentModuleIndex = course.modules.findIndex(
			(m: any) => m.order === currentModuleOrder
		);
		if (currentModuleIndex === -1) return;

		const currentModule = course.modules[currentModuleIndex];
		const currentLessonIndex = currentModule.lessons.findIndex(
			(l: any) => l.order === currentLessonOrder
		);
		if (currentLessonIndex === -1) return;

		// Se não é a última aula do módulo
		if (currentLessonIndex < currentModule.lessons.length - 1) {
			const nextLesson = currentModule.lessons[currentLessonIndex + 1];
			navigateToLesson(currentModule.order, nextLesson.order);
			return;
		}

		// Se é a última aula do módulo, mas não o último módulo
		if (currentModuleIndex < course.modules.length - 1) {
			const nextModule = course.modules[currentModuleIndex + 1];
			if (nextModule.lessons.length > 0) {
				navigateToLesson(nextModule.order, nextModule.lessons[0].order);
			}
		}
	};

	// Função para navegar para a aula anterior
	const navigateToPreviousLesson = () => {
		if (
			!course ||
			currentModuleOrder === undefined ||
			currentLessonOrder === undefined
		)
			return;

		const currentModuleIndex = course.modules.findIndex(
			(m: any) => m.order === currentModuleOrder
		);
		if (currentModuleIndex === -1) return;

		const currentModule = course.modules[currentModuleIndex];
		const currentLessonIndex = currentModule.lessons.findIndex(
			(l: any) => l.order === currentLessonOrder
		);
		if (currentLessonIndex === -1) return;

		// Se não é a primeira aula do módulo
		if (currentLessonIndex > 0) {
			const prevLesson = currentModule.lessons[currentLessonIndex - 1];
			navigateToLesson(currentModule.order, prevLesson.order);
			return;
		}

		// Se é a primeira aula do módulo, mas não o primeiro módulo
		if (currentModuleIndex > 0) {
			const prevModule = course.modules[currentModuleIndex - 1];
			if (prevModule.lessons.length > 0) {
				const lastLesson = prevModule.lessons[prevModule.lessons.length - 1];
				navigateToLesson(prevModule.order, lastLesson.order);
			}
		}
	};

	const { markAsCompleted, markAsViewed, isCompleting, isViewing } = useLessonCompletion(
		courseId,
		currentModuleOrder,
		currentLessonOrder
	);

	// Marcar como visualizada quando a aula muda (com debounce)
	useEffect(() => {
		if (currentModuleOrder !== undefined && currentLessonOrder !== undefined && !isViewing) {
			const timer = setTimeout(() => {
				markAsViewed().catch(console.error);
			}, 1000); // Debounce de 1 segundo

			return () => clearTimeout(timer);
		}
	}, [currentModuleOrder, currentLessonOrder, isViewing]);

	// Função para marcar como concluída com feedback
	const handleMarkAsCompleted = async () => {
		try {
			const result = await markAsCompleted();
			if (result?.success) {
				toast({
					title: 'Aula concluída!',
					description: 'Parabéns! Você concluiu esta aula.',
				});
				// Recarregar dados do curso para atualizar o progresso
				window.location.reload();
			}
		} catch (error) {
			console.error('Error marking lesson as completed:', error);
			toast({
				title: 'Erro',
				description: 'Não foi possível marcar a aula como concluída.',
				variant: 'error',
			});
		}
	};

	// Carregar comentários da aula do localStorage
	useEffect(() => {
		if (currentModuleOrder !== undefined && currentLessonOrder !== undefined) {
			const lessonKey = `lesson-comments-${courseId}-${currentModuleOrder}-${currentLessonOrder}`;
			const savedComments = localStorage.getItem(lessonKey);
			if (savedComments) {
				try {
					const parsed = JSON.parse(savedComments);
					setComments(parsed.map((c: any) => ({
						...c,
						createdAt: new Date(c.createdAt)
					})));
				} catch (error) {
					console.error('Error parsing comments:', error);
					setComments([]);
				}
			} else {
				setComments([]);
			}
		}
	}, [courseId, currentModuleOrder, currentLessonOrder]);

	// Função para enviar comentário
	const handleSubmitComment = () => {
		if (!comment.trim() || currentModuleOrder === undefined || currentLessonOrder === undefined) return;

		const newComment = {
			id: Date.now().toString(),
			text: comment.trim(),
			author: 'Você', // Em uma implementação real, seria o nome do usuário logado
			createdAt: new Date()
		};

		const updatedComments = [newComment, ...comments];
		setComments(updatedComments);

		// Salvar no localStorage
		const lessonKey = `lesson-comments-${courseId}-${currentModuleOrder}-${currentLessonOrder}`;
		localStorage.setItem(lessonKey, JSON.stringify(updatedComments));

		toast({
			title: 'Comentário enviado!',
			description: 'Seu comentário foi registrado com sucesso.',
		});
		setComment('');
	};

	// Calculate current module and lesson index
	const getCurrentModuleAndLessonIndex = () => {
		if (
			!course ||
			currentModuleOrder === undefined ||
			currentLessonOrder === undefined
		) {
			return { moduleIndex: 0, lessonIndex: 0 };
		}

		const moduleIndex = course.modules.findIndex(
			(m: any) => m.order === currentModuleOrder
		);
		if (moduleIndex === -1) return { moduleIndex: 0, lessonIndex: 0 };

		const lessonIndex = course.modules[moduleIndex].lessons.findIndex(
			(lesson: any) => lesson.order === currentLessonOrder
		);
		if (lessonIndex === -1) return { moduleIndex: moduleIndex, lessonIndex: 0 };

		return { moduleIndex, lessonIndex };
	};

	const { moduleIndex, lessonIndex } = getCurrentModuleAndLessonIndex();
	const currentModule = course?.modules[moduleIndex];

	// Calculate progress
	const calculateProgress = () => {
		if (!course) return 0;

		const totalLessons = course.modules.reduce(
			(acc: number, module: any) => acc + module.lessons.length,
			0
		);

		const completedLessons = course.modules.reduce(
			(acc: number, module: any) =>
				acc + module.lessons.filter((lesson: any) => lesson.completed).length,
			0
		);

		return totalLessons > 0
			? Math.round((completedLessons / totalLessons) * 100)
			: 0;
	};

	const progress = calculateProgress();

	// Check if it's the first or last lesson
	const isFirstLesson = moduleIndex === 0 && lessonIndex === 0;
	const isLastLesson =
		moduleIndex === (course?.modules.length || 0) - 1 &&
		lessonIndex === (currentModule?.lessons.length || 0) - 1;

	if (isLoading) {
		return (
			<div className='min-h-screen bg-gray-50'>
				<div className='flex flex-col space-y-6 p-4 md:p-8'>
					<div className='flex items-center space-x-4'>
						<Skeleton className='h-12 w-12 rounded-full' />
						<div className='space-y-2'>
							<Skeleton className='h-4 w-[250px]' />
							<Skeleton className='h-4 w-[200px]' />
						</div>
					</div>
					<Skeleton className='h-[300px] w-full rounded-xl' />
					<div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
						<Skeleton className='h-[200px] w-full rounded-xl' />
						<Skeleton className='h-[200px] w-full rounded-xl' />
						<Skeleton className='h-[200px] w-full rounded-xl' />
					</div>
				</div>
			</div>
		);
	}

	if (!course) {
		return (
			<div className='min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4'>
				<div className='text-center space-y-4'>
					<div className='w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto'>
						<BookOpen className='w-8 h-8 text-gray-400' />
					</div>
					<div className='space-y-2'>
						<h1 className='text-xl font-semibold text-gray-900'>Curso não encontrado</h1>
						<p className='text-gray-600'>O curso que você está procurando não existe ou foi removido.</p>
					</div>
					<Button asChild>
						<Link href='/account'>Voltar para Meus Cursos</Link>
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div className='min-h-screen bg-gray-50 flex flex-col'>
			{/* Mobile-First Header */}
			<header className='bg-white border-b border-gray-200 sticky top-0 z-30'>
				<div className='px-4 py-4 md:px-6 md:py-5'>
					<div className='flex items-center justify-between gap-3'>
						<div className='flex items-center gap-3 min-w-0 flex-1'>
							<Link
								href={`/account/course/${courseId}`}
								className='flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors flex-shrink-0'
							>
								<ArrowLeft className='w-4 h-4' />
								<span className='text-sm font-medium hidden sm:inline'>Voltar</span>
							</Link>

							<div className='min-w-0 flex-1'>
								<h1 className='text-base md:text-lg font-semibold text-gray-900 truncate'>
									{currentLesson?.title || 'Carregando...'}
								</h1>
								<p className='text-sm text-gray-500 truncate'>
									{course.product.title}
								</p>
							</div>
						</div>

						<div className='flex items-center gap-2'>
							{/* Desktop Sidebar Toggle */}
							<Button
								variant='ghost'
								size='sm'
								onClick={() => setShowSidebar(!showSidebar)}
								className='hidden md:flex'
							>
								{showSidebar ? (
									<PanelRightClose className='w-4 h-4' />
								) : (
									<PanelRightOpen className='w-4 h-4' />
								)}
							</Button>

							{/* Mobile Sidebar Toggle */}
							<Button
								variant='ghost'
								size='sm'
								onClick={() => setShowMobileSidebar(true)}
								className='md:hidden'
							>
								<Menu className='w-4 h-4' />
							</Button>
						</div>
					</div>
				</div>
			</header>

			<div className='flex flex-1 relative'>
				{/* Main Content */}
				<div className={cn(
					'flex-1 transition-all duration-300',
					// showSidebar ? 'md:mr-80' : 'md:mr-0'
				)}>
					<main className='w-full h-full overflow-y-auto'>
												<div className='min-h-full flex justify-center items-start'>
							<div className='w-full max-w-5xl mx-auto space-y-6 py-4 md:py-6 px-4 md:px-8'>
							{/* Video Player - Optimized Centering */}
														{currentLesson?.videoUrl ? (
								<div className='aspect-video bg-black rounded-lg overflow-hidden shadow-lg w-full  mx-auto'>
									<VideoPlayer
										url={currentLesson.videoUrl}
										onComplete={() => {
											handleMarkAsCompleted();
										}}
									/>
								</div>
														) : (
								<div className='aspect-video bg-gray-900 rounded-lg flex items-center justify-center w-full max-w-4xl mx-auto'>
									<div className='text-center text-white space-y-3'>
										<PlayCircle className='w-16 h-16 mx-auto opacity-50' />
										<div className='space-y-1'>
											<p className='text-lg font-medium'>Vídeo não disponível</p>
											<p className='text-sm text-gray-300'>
												O conteúdo desta aula ainda não foi carregado.
											</p>
										</div>
									</div>
								</div>
							)}

							{/* Lesson Navigation */}
							<div className='flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-4 bg-white p-4 rounded-lg border border-gray-200 shadow-sm max-w-5xl mx-auto'>
								<Button
									variant='outline'
									size='sm'
									onClick={navigateToPreviousLesson}
									disabled={isFirstLesson}
									className='flex-1 sm:flex-none min-h-[48px] sm:min-h-[40px] text-base sm:text-sm'
								>
									<ArrowLeft className='w-4 h-4 mr-2' />
									<span className='font-medium'>Anterior</span>
								</Button>

								<div className='flex items-center justify-center gap-3 order-first sm:order-none'>
									{currentLesson?.completed ? (
										<div className='flex items-center gap-2 text-emerald-600 bg-emerald-50 px-4 py-3 rounded-full border border-emerald-200'>
											<CheckCircle className='w-5 h-5' />
											<span className='text-sm font-medium'>Concluída</span>
										</div>
									) : (
										<Button
											size='sm'
											className='bg-primary hover:bg-primary/90 min-h-[40px] px-6 text-base sm:text-sm'
											onClick={handleMarkAsCompleted}
											disabled={isCompleting}
										>
											{isCompleting ? (
												<>
													<Loader2 className='w-4 h-4 mr-2 animate-spin' />
													<span>Salvando...</span>
												</>
											) : (
												<>
													<CheckCircle className='w-4 h-4 mr-2' />
													<span>Concluir Aula</span>
												</>
											)}
										</Button>
									)}
								</div>

								<Button
									variant='outline'
									size='sm'
									onClick={navigateToNextLesson}
									disabled={isLastLesson}
									className='flex-1 sm:flex-none min-h-[48px] sm:min-h-[40px] text-base sm:text-sm'
								>
									<span className='font-medium'>Próxima</span>
									<ArrowRight className='w-4 h-4 ml-2' />
								</Button>
							</div>

							{/* Lesson Content */}
							<div className='space-y-6 max-w-5xl mx-auto'>
								{/* Lesson Info */}
								<Card>
									<CardHeader>
										<CardTitle className='text-xl'>
											{currentLesson?.title || 'Carregando...'}
										</CardTitle>
										{currentLesson?.duration && (
											<div className='flex items-center gap-2 text-sm text-gray-600'>
												<Clock className='w-4 h-4' />
												<span>{Math.round((currentLesson.duration || 0) / 60)} minutos</span>
											</div>
										)}
									</CardHeader>
									<CardContent>
										<div className='prose prose-sm max-w-none text-gray-700'>
											{currentLesson?.description ||
												'Nenhuma descrição disponível para esta aula.'}
										</div>
									</CardContent>
								</Card>

								{/* Comments Section */}
								<Card>
									<CardHeader>
										<CardTitle className='text-lg flex items-center gap-2'>
											<MessageSquare className='w-5 h-5' />
											Comentários ({comments.length})
										</CardTitle>
									</CardHeader>
									<CardContent>
										<div className='space-y-4'>
											{/* Add Comment Form */}
											<div className='space-y-3'>
												<Textarea
													placeholder='Deixe seu comentário sobre esta aula...'
													value={comment}
													onChange={(e) => setComment(e.target.value)}
													className='min-h-[100px] resize-none'
												/>
												<div className='flex justify-end'>
													<Button
														onClick={handleSubmitComment}
														disabled={!comment.trim()}
														size='sm'
													>
														<Send className='w-4 h-4 mr-2' />
														Enviar Comentário
													</Button>
												</div>
											</div>

											{/* Comments List */}
											{comments.length > 0 ? (
												<div className='space-y-4 border-t pt-4'>
													{comments.map((commentItem) => (
														<div key={commentItem.id} className='bg-gray-50 rounded-lg p-4'>
															<div className='flex items-start gap-3'>
																<Avatar className='w-8 h-8'>
																	<AvatarFallback className='bg-primary text-white text-xs'>
																		{commentItem.author.charAt(0)}
																	</AvatarFallback>
																</Avatar>
																<div className='flex-1 min-w-0'>
																	<div className='flex items-center gap-2 mb-2'>
																		<span className='font-medium text-sm text-gray-900'>
																			{commentItem.author}
																		</span>
																		<span className='text-xs text-gray-500'>
																			{commentItem.createdAt.toLocaleDateString('pt-BR', {
																				day: '2-digit',
																				month: '2-digit',
																				year: 'numeric',
																				hour: '2-digit',
																				minute: '2-digit'
																			})}
																		</span>
																	</div>
																	<p className='text-sm text-gray-700 whitespace-pre-wrap'>
																		{commentItem.text}
																	</p>
																</div>
															</div>
														</div>
													))}
												</div>
											) : (
												<div className='text-sm text-gray-500 text-center py-8 border-t'>
													<MessageSquare className='w-8 h-8 mx-auto mb-2 text-gray-300' />
													<p>Seja o primeiro a comentar nesta aula!</p>
												</div>
											)}
										</div>
									</CardContent>
								</Card>

								{/* Lesson Resources - Only show if available */}
								{currentLesson?.resources && currentLesson.resources.length > 0 && (
									<Card>
										<CardHeader>
											<CardTitle className='text-lg'>Materiais da Aula</CardTitle>
										</CardHeader>
										<CardContent>
											<div className='space-y-3'>
												{currentLesson.resources.map((resource: any, index: number) => {
													const getIcon = (type: string) => {
														switch (type) {
															case 'pdf':
																return FileText;
															case 'link':
																return Link2;
															default:
																return Download;
														}
													};
													const Icon = getIcon(resource.type);

													return (
														<div
															key={index}
															className='flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer group'
														>
															<div className='p-2 bg-primary/10 rounded-full text-primary group-hover:bg-primary/20'>
																<Icon className='w-4 h-4' />
															</div>
															<div className='flex-1 min-w-0'>
																<p className='font-medium text-sm truncate'>{resource.title}</p>
																<p className='text-xs text-gray-500'>
																	{resource.type === 'pdf' ? 'PDF Document' :
																	 resource.type === 'link' ? 'Link Externo' :
																	 'Material Complementar'}
																</p>
															</div>
															<Button size='sm' variant='ghost' asChild>
																<a
																	href={resource.url}
																	target='_blank'
																	rel='noopener noreferrer'
																>
																	<ExternalLink className='w-3 h-3' />
																</a>
															</Button>
														</div>
													);
												})}
											</div>
										</CardContent>
									</Card>
								)}
							</div>
						</div>
					</div>
					</main>
				</div>

				{/* Course Content Sidebar - Optimized Width */}
				<aside
					className={cn(
						'fixed top-0 right-0 bottom-0 w-full md:w-96 bg-white border-l border-gray-200 overflow-y-auto transition-all duration-300 z-20',
						// Mobile
						showMobileSidebar ? 'translate-x-0' : 'translate-x-full',
						// Desktop
						'md:relative md:translate-x-0',
						showSidebar ? 'md:block' : 'md:hidden'
					)}
				>
					<div className='p-3 md:p-5'>
						{/* Mobile Header */}
						<div className='flex items-center justify-between mb-6 md:hidden'>
							<h3 className='font-semibold text-lg'>Conteúdo do Curso</h3>
							<Button
								variant='ghost'
								size='sm'
								onClick={() => setShowMobileSidebar(false)}
							>
								<X className='w-4 h-4' />
							</Button>
						</div>

						{/* Desktop Header */}
						<div className='hidden md:block mb-6'>
							<h3 className='font-semibold text-lg'>Conteúdo do Curso</h3>
						</div>

						{/* Course Info */}
						<div className='mb-6 p-4 bg-gradient-to-br from-primary/5 to-primary/10 rounded-lg border border-primary/20'>
							<h4 className='font-semibold text-gray-900 mb-3 text-sm leading-tight break-words'>{course.product.title}</h4>
							<div className='text-sm text-gray-600 space-y-2'>
								<div className='flex items-center justify-between'>
									<span>{course.modules.length} módulos</span>
									<span>{course.modules.reduce((acc: number, module: any) => acc + module.lessons.length, 0)} aulas</span>
								</div>
								<div className='space-y-2'>
									<div className='flex items-center justify-between text-xs'>
										<span>Progresso</span>
										<span className='font-medium'>{progress}%</span>
									</div>
									<div className='w-full bg-gray-200 rounded-full h-2'>
										<div
											className='bg-primary h-2 rounded-full transition-all duration-300'
											style={{ width: `${progress}%` }}
										/>
									</div>
								</div>
							</div>
						</div>

						{/* Modules */}
						<div className='space-y-3'>
							{course.modules.map((module: any) => (
								<ModuleAccordion
									key={module.order}
									module={module}
									currentModuleOrder={currentModuleOrder}
									currentLessonOrder={currentLessonOrder}
									onLessonSelect={(moduleOrder, lessonOrder) => {
										navigateToLesson(moduleOrder, lessonOrder);
										// Auto-close sidebar on mobile after selection
										if (typeof window !== 'undefined' && window.innerWidth < 768) {
											setShowMobileSidebar(false);
										}
									}}
									expanded={module.order === currentModuleOrder}
								/>
							))}
						</div>
					</div>
				</aside>
			</div>

			{/* Mobile Overlay */}
			{showMobileSidebar && (
				<div
					className='fixed inset-0 bg-black/50 z-10 md:hidden'
					onClick={() => setShowMobileSidebar(false)}
				/>
			)}
		</div>
	);
}
