'use client';

import { useState } from 'react';
import { But<PERSON> } from '@ui/components/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/card';
import { Badge } from '@ui/components/badge';
import { RadioGroup, RadioGroupItem } from '@ui/components/radio-group';
import { Label } from '@ui/components/label';
import { CreditCard, Loader2, Lock, Zap } from 'lucide-react';
import { formatCurrency } from 'utils';

interface OneClickPurchaseProps {
  amount: number;
  onSubmit: (data: { paymentMethod: 'CREDIT_CARD' | 'PIX' | 'BOLETO' }) => Promise<void>;
  isLoading?: boolean;
  submitButtonText?: string;
  hasStoredCard?: boolean;
  showPaymentOptions?: boolean;
}

export function OneClickPurchase({
  amount,
  onSubmit,
  isLoading = false,
  submitButtonText = 'Comprar Agora',
  hasStoredCard = true,
  showPaymentOptions = true,
}: OneClickPurchaseProps) {
  const [selectedMethod, setSelectedMethod] = useState<'CREDIT_CARD' | 'PIX' | 'BOLETO'>('CREDIT_CARD');

  const handleOneClickPurchase = async () => {
    try {
      await onSubmit({ paymentMethod: selectedMethod });
    } catch (error) {
      console.error('One-click purchase error:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* One-Click Purchase for Credit Card */}
      {hasStoredCard && selectedMethod === 'CREDIT_CARD' && (
        <Card className="border-green-200 bg-green-50">
          <CardHeader className="pb-4">
            <div className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-green-600" />
              <CardTitle className="text-green-800">Compra com 1 Clique</CardTitle>
            </div>
            <CardDescription className="text-green-600">
              Use o mesmo cartão da compra anterior para finalizar instantaneamente
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
                <div className="flex items-center gap-3">
                  <CreditCard className="h-5 w-5 text-gray-600" />
                  <div>
                    <p className="font-medium">Cartão salvo</p>
                    <p className="text-sm text-gray-600">**** **** **** ****</p>
                  </div>
                </div>
                <Badge>Seguro</Badge>
              </div>
              
              <div className="flex justify-between items-center text-lg font-semibold">
                <span>Total:</span>
                <span className="text-green-600">{formatCurrency(amount)}</span>
              </div>

              <Button 
                onClick={handleOneClickPurchase} 
                className="w-full h-12 bg-green-600 hover:bg-green-700" 
                disabled={isLoading}
              >
                {isLoading ? (
                  <span className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Processando...
                  </span>
                ) : (
                  <span className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    {submitButtonText}
                  </span>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alternative Payment Methods */}
      {showPaymentOptions && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              {hasStoredCard ? 'Ou escolha outro método' : 'Escolha o método de pagamento'}
            </CardTitle>
            <CardDescription>
              {hasStoredCard 
                ? 'Prefere usar outro método de pagamento?' 
                : 'Selecione como deseja pagar'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Payment Method Selection */}
            <div className="space-y-4">
              <Label className="text-base font-medium">Método de Pagamento</Label>
              <RadioGroup
                value={selectedMethod}
                onValueChange={(value: 'CREDIT_CARD' | 'PIX' | 'BOLETO') => {
                  setSelectedMethod(value);
                }}
                className="grid grid-cols-3 gap-3"
              >
                <div className="flex items-center space-x-2 border rounded-lg p-3">
                  <RadioGroupItem value="CREDIT_CARD" id="credit-card" />
                  <Label htmlFor="credit-card" className="flex-1 cursor-pointer">
                    <div className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      <span className="text-sm">Cartão</span>
                    </div>
                  </Label>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-3">
                  <RadioGroupItem value="PIX" id="pix" />
                  <Label htmlFor="pix" className="flex-1 cursor-pointer">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-green-500 rounded-sm"></div>
                      <span className="text-sm">PIX</span>
                    </div>
                  </Label>
                </div>
                <div className="flex items-center space-x-2 border rounded-lg p-3">
                  <RadioGroupItem value="BOLETO" id="boleto" />
                  <Label htmlFor="boleto" className="flex-1 cursor-pointer">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-orange-500 rounded-sm"></div>
                      <span className="text-sm">Boleto</span>
                    </div>
                  </Label>
                </div>
              </RadioGroup>
            </div>

            {/* PIX Info */}
            {selectedMethod === 'PIX' && (
              <div className="text-center space-y-2 p-4 bg-green-50 rounded-lg">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <div className="w-6 h-6 bg-green-500 rounded-sm"></div>
                </div>
                <h3 className="font-medium">Pagamento via PIX</h3>
                <p className="text-sm text-gray-600">
                  Após confirmar, você receberá o QR Code para pagamento instantâneo
                </p>
              </div>
            )}

            {/* Boleto Info */}
            {selectedMethod === 'BOLETO' && (
              <div className="text-center space-y-2 p-4 bg-orange-50 rounded-lg">
                <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto">
                  <div className="w-6 h-6 bg-orange-500 rounded-sm"></div>
                </div>
                <h3 className="font-medium">Pagamento via Boleto</h3>
                <p className="text-sm text-gray-600">
                  Confirmação em até 24 horas após o pagamento
                </p>
              </div>
            )}

            {/* Total and Submit for non-credit card methods */}
            {(selectedMethod !== 'CREDIT_CARD' || !hasStoredCard) && (
              <div className="space-y-4">
                <div className="flex justify-between items-center text-lg font-semibold">
                  <span>Total:</span>
                  <span>{formatCurrency(amount)}</span>
                </div>

                <Button 
                  onClick={handleOneClickPurchase} 
                  className="w-full h-12" 
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Processando...
                    </span>
                  ) : (
                    <span className="flex items-center gap-2">
                      <Lock className="h-4 w-4" />
                      {selectedMethod === 'PIX'
                        ? 'Gerar QR Code PIX'
                        : selectedMethod === 'BOLETO'
                          ? 'Gerar Boleto'
                          : submitButtonText
                      }
                    </span>
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
