import "server-only";
import { AsaasClient } from 'api/modules/checkout/asaas';
import { db } from 'database';
import { redirect } from 'next/navigation';
import { CheckoutHeader } from '../components/checkout-header';
import { BoletoPage } from './page-client';

interface BoletoPageProps {
	searchParams: Promise<{
		orderId: string;
	}>;
}

export default async function CheckoutBoletoPage({
	searchParams,
}: BoletoPageProps) {
	// Aguardar os parâmetros antes de acessá-los
	const resolvedSearchParams = await searchParams;
	console.log('searchParams', resolvedSearchParams);

	if (!resolvedSearchParams.orderId) {
		redirect('/');
	}

	try {
		// Buscar dados do pedido
		const order = await db.order.findUnique({
			where: { id: resolvedSearchParams.orderId },
			include: {
				product: true,
				user: true,
			},
		});

		console.log('order', order);

		if (!order) {
			redirect('/');
		}

		// Buscar dados do boleto
		const asaas = new AsaasClient();
		const boletoData = await asaas.getBankSlipUrl(order.gatewayId || '');
		const identificationData = await asaas.getIdentificationField(
			order.gatewayId || ''
		);

		return (
			<div className='min-h-screen bg-gray-50/50'>
				<CheckoutHeader />

				<div className='container max-w-5xl py-8 lg:py-12'>
					<BoletoPage
						data={{
							order,
							bankSlipUrl: boletoData.bankSlipUrl,
							identificationField: identificationData.identificationField,
							barCode: identificationData.barCode,
						}}
					/>
				</div>
			</div>
		);
	} catch (error) {
		console.error('Error loading Boleto page:', error);
		redirect('/');
	}
}
