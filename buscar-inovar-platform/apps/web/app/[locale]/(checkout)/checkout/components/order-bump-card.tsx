// components/order-bump-card.tsx
import { ArrowRight, Check, ChevronRight } from 'lucide-react';
import Image from 'next/image';
import { Offer } from './types';
import {
	IconArrowBigRightFilled,
	IconArrowBigRightLinesFilled,
} from '@tabler/icons-react';

interface OrderBumpCardProps {
	offer: Offer;
	checked: boolean;
	onToggle: (checked: boolean) => void;
}

export function OrderBumpCard({
	offer,
	checked,
	onToggle,
}: OrderBumpCardProps) {
	return (
		<div className='border-green-400 border-2  border-dotted rounded-sm overflow-hidden  bg-white hover:border-green-600 transition-colors'>
			<div className='flex gap-4 p-4'>
				{/* Thumbnail */}
				<div className='flex-shrink-0 w-16 h-16 relative'>
					{offer.thumbnail ? (
						<Image
							src={offer.thumbnail}
							alt={offer.title || ''}
							width={64}
							height={64}
							className='rounded-lg object-cover'
						/>
					) : (
						<div className='w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center'>
							<Check className='w-6 h-6 text-gray-400' />
						</div>
					)}
				</div>

				{/* Content */}
				<div className='flex-1'>
					<h3 className='font-medium text-lg'>
						{offer.title || 'Oferta Especial'}
					</h3>
					<p className='text-sm text-gray-600 mt-1'>
						{offer.description || ''}
					</p>
					<div className='mt-2 flex items-center justify-between'>
						<div className='flex items-center gap-2'>
							<span className='text-lg font-bold text-primary'>
								R$ {Number(offer.price).toFixed(2)}
							</span>
						</div>
					</div>
				</div>
			</div>
			<div className='w-full bg-green-400/50 p-3'>
				<label className='flex items-center gap-2 cursor-pointer'>
					<IconArrowBigRightLinesFilled className='w-7 h-7 text-primary' />

					<input
						type='checkbox'
						checked={checked}
						onChange={(e) => onToggle(e.target.checked)}
						className='w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary'
					/>
					<span className='text-sm font-medium'>Adicionar oferta</span>
				</label>
			</div>
		</div>
	);
}
