// components/phone-input-international.tsx
'use client';

import { cn } from '@ui/lib';
import { forwardRef, useRef, useEffect, useState } from 'react';
import { Input } from '@ui/components/input';
import dynamic from 'next/dynamic';
import 'react-international-phone/style.css';

// Importar o componente PhoneInput dinamicamente para evitar problemas de SSR
const PhoneInput = dynamic(
	() => import('react-international-phone').then((mod) => mod.PhoneInput),
	{
		ssr: false,
	}
);

interface InternationalPhoneInputProps
	extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
	value: string;
	onChange?: (value: string) => void;
	error?: boolean;
	errorMessage?: string;
}

export const InternationalPhoneInput = forwardRef<
	HTMLInputElement,
	InternationalPhoneInputProps
>(({ className, value, onChange, error, errorMessage, ...props }, ref) => {
	// Estado para controlar se o componente está montado no cliente
	const [isMounted, setIsMounted] = useState(false);

	// Efeito para marcar o componente como montado no cliente
	useEffect(() => {
		setIsMounted(true);
	}, []);

	// Usamos um ref para o input
	const phoneInputRef = useRef<any>(null);

	// Função para formatar o número de telefone no padrão brasileiro
	const formatBrazilianPhoneNumber = (phone: string): string => {
		// Remove todos os caracteres não numéricos
		const numericOnly = phone.replace(/\D/g, '');

		// Se começar com 55 (código do Brasil), remove para formatação local
		const localNumber = numericOnly.startsWith('55')
			? numericOnly.substring(2)
			: numericOnly;

		// Formata de acordo com o tamanho
		if (localNumber.length <= 10) {
			// Telefone fixo: (XX) XXXX-XXXX
			return localNumber.replace(/^(\d{2})(\d{4})(\d{0,4}).*/, '($1) $2-$3');
		} else {
			// Celular: (XX) XXXXX-XXXX
			return localNumber.replace(/^(\d{2})(\d{5})(\d{0,4}).*/, '($1) $2-$3');
		}
	};

	// Sincronizar a ref interna com a ref externa, se fornecida
	useEffect(() => {
		if (phoneInputRef.current && phoneInputRef.current.inputRef) {
			if (ref && typeof ref === 'function') {
				ref(phoneInputRef.current.inputRef);
			} else if (ref) {
				ref.current = phoneInputRef.current.inputRef;
			}
		}
	}, [ref, phoneInputRef.current]);
	return (
		<div className='space-y-1'>
			<div className='relative'>
				{isMounted ? (
					<>
						<PhoneInput
							defaultCountry='br'
							value={value}
							placeholder='(00) 00000-0000'
							prefix='+'
							preferredCountries={['br', 'us', 'pt']}
							charAfterDialCode=' '
							forceDialCode={true}
							disableDialCodeAndPrefix={false}
							defaultMask='(..) .....-....'
							onChange={(phone) => {
								// Se for um número brasileiro, formata no padrão brasileiro
								if (phone.startsWith('55')) {
									// Mantém o +55 no início
									const formattedPhone =
										'+55' +
										formatBrazilianPhoneNumber(phone).replace(/[()\s-]/g, '');
									onChange?.(formattedPhone);
								} else {
									onChange?.(phone);
								}
							}}
							inputProps={{
								...props,
								required: true,
							}}
							ref={phoneInputRef}
							className={cn(
								'phone-input-container',
								error && 'phone-input-error',
								className
							)}
						/>
					</>
				) : (
					<Input
						placeholder='(00) 00000-0000'
						disabled
						className={cn(
							'flex h-10 w-full rounded-md border border-input bg-transparent pl-3 py-1 text-base',
							error && 'border-destructive',
							className
						)}
					/>
				)}
			</div>
			{/* {error && errorMessage && (
				<p className='text-sm font-medium text-destructive'>{errorMessage}</p>
			)} */}
		</div>
	);
});

InternationalPhoneInput.displayName = 'InternationalPhoneInput';
