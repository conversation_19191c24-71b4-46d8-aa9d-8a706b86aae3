// components/checkout-header.tsx
import Link from 'next/link';
import { Lock } from 'lucide-react';
import { Logo } from '@shared/components/Logo';

export function CheckoutHeader() {
	return (
		<header className='sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60'>
			<div className='container flex h-16 items-center justify-between'>
				<Link href='/' className='flex items-center space-x-2'>
					<Logo />
				</Link>

				<div className='flex items-center space-x-2 text-xs text-muted-foreground'>
					<Lock className='h-4 w-4' />
					<span>Ambiente seguro</span>
				</div>
			</div>
		</header>
	);
}
