// components/credit-card-input.tsx

import { cn } from '@ui/lib';
import Cleave from 'cleave.js/react';
import { CreditCard } from 'lucide-react';
import { forwardRef } from 'react';
import { FaCcAmex, FaCcMastercard, FaCcVisa } from 'react-icons/fa6';

interface CreditCardInputProps
	extends Omit<
		React.InputHTMLAttributes<HTMLInputElement>,
		'value' | 'onChange'
	> {
	value?: string;
	onChange?: (value: string) => void;
	onCardTypeChange?: (type: CardType) => void;
	error?: boolean;
}

type CardType = 'visa' | 'mastercard' | 'amex' | 'elo' | '';

export const CreditCardInput = forwardRef<
	HTMLInputElement,
	CreditCardInputProps
>(
	(
		{ className, error, value = '', onChange, onCardTypeChange, ...props },
		ref
	) => {
		const detectCardType = (number: string): CardType => {
			const cleaned = number.replace(/\D/g, '');

			// Visa
			if (/^4/.test(cleaned)) return 'visa';

			// Mastercard
			if (/^5[1-5]/.test(cleaned)) return 'mastercard';

			// American Express
			if (/^3[47]/.test(cleaned)) return 'amex';

			// Elo
			if (/^(636368|438935|504175|451416|636297|5067|4576|4011)/.test(cleaned))
				return 'elo';

			return '';
		};

		const handleChange = (e: any) => {
			const rawValue = e.target.rawValue;
			const type = detectCardType(rawValue);
			onCardTypeChange?.(type);
			onChange?.(rawValue);
		};

		const CardIcon = ({ type }: { type: CardType }) => {
			switch (type) {
				case 'visa':
					return <FaCcVisa className='h-5 w-5' />;
				case 'mastercard':
					return <FaCcMastercard className='h-5 w-5' />;
				case 'amex':
					return <FaCcAmex className='h-5 w-5' />;
				case 'elo':
					return <CreditCard className='h-5 w-5' />;
				default:
					return <CreditCard className='h-5 w-5 text-muted-foreground' />;
			}
		};

		return (
			<div className='relative'>
				<Cleave
					{...props}
					type='tel'
					inputMode='numeric'
					className={cn(
						'flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base transition-colors file:border-0 file:bg-transparent file:font-medium file:text-sm placeholder:text-muted-foreground focus-visible:border-primary focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
						'pl-10',
						error && 'border-destructive',
						className
					)}
					options={{
						creditCard: true,
						onCreditCardTypeChanged: (type) => {
							onCardTypeChange?.(type as CardType);
						},
					}}
					placeholder='Digite o número do cartão'
					value={value}
					onChange={handleChange}
					ref={ref}
				/>
				<div className='absolute left-3 top-1/2 -translate-y-1/2'>
					<CardIcon type={detectCardType(value)} />
				</div>
			</div>
		);
	}
);

CreditCardInput.displayName = 'CreditCardInput';
