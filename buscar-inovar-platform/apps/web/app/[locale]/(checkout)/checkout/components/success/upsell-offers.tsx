// components/upsell-offers.tsx
import { BadgeCheck } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Button } from '@ui/components/button';
import Link from 'next/link';
import { formatCurrency } from 'utils';

interface UpsellOffersProps {
	offers: Array<{
		id: string;
		title: string;
		description?: string | null;
		price: number;
		thumbnail?: string | null;
	}>;
}

export function UpsellOffers({ offers }: UpsellOffersProps) {
	if (!offers.length) return null;

	return (
		<Card className='mt-8'>
			<CardHeader className='pb-3'>
				<div className='flex items-center gap-2'>
					<BadgeCheck className='h-5 w-5 text-primary' />
					<CardTitle>Ofertas Especiais</CardTitle>
				</div>
			</CardHeader>

			<CardContent>
				<div className='grid gap-4'>
					{offers.map((offer) => (
						<div key={offer.id} className='relative rounded-lg border p-4'>
							<div className='flex items-start gap-4'>
								{/* Thumbnail */}
								{offer.thumbnail ? (
									<div className='relative h-16 w-16 shrink-0 overflow-hidden rounded-lg'>
										<img
											src={offer.thumbnail}
											alt={offer.title}
											className='h-full w-full object-cover'
										/>
									</div>
								) : (
									<div className='flex h-16 w-16 shrink-0 items-center justify-center rounded-lg bg-muted'>
										<BadgeCheck className='h-8 w-8 text-muted-foreground' />
									</div>
								)}

								<div className='flex-1 space-y-1'>
									{/* Preço Original vs Preço da Oferta */}
									<div className='flex items-baseline gap-2'>
										<span className='text-sm font-medium text-muted-foreground line-through'>
											{formatCurrency(offer.price * 1.5)}
										</span>
										<span className='text-lg font-bold text-primary'>
											{formatCurrency(offer.price)}
										</span>
									</div>

									{/* Título e Descrição */}
									<h4 className='font-medium'>{offer.title}</h4>
									{offer.description && (
										<p className='text-sm text-muted-foreground'>
											{offer.description}
										</p>
									)}

									<Button asChild variant='outline' className='mt-4'>
										<Link href={`/checkout/${offer.id}`}>
											Aproveitar oferta
										</Link>
									</Button>
								</div>
							</div>
						</div>
					))}
				</div>
			</CardContent>
		</Card>
	);
}
