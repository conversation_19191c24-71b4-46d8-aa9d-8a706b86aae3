// components/pix-qrcode.tsx
import Image from 'next/image';
import { Card } from '@ui/components/card';

interface PixQRCodeProps {
	qrCodeBase64: string;
	size?: number;
}

export function PixQRCode({ qrCodeBase64, size = 200 }: PixQRCodeProps) {
	if (!qrCodeBase64) {
		return (
			<Card className='flex h-[200px] w-[200px] items-center justify-center'>
				<span className='text-sm text-muted-foreground'>
					Erro ao carregar QR Code
				</span>
			</Card>
		);
	}

	return (
		<Image
			src={`data:image/png;base64,${qrCodeBase64}`}
			alt='QR Code PIX'
			width={size}
			height={size}
			className='rounded-lg'
		/>
	);
}
