// components/order-bump-list.tsx
import { OrderBumpCard } from './order-bump-card';
import { Offer } from './types';

interface OrderBumpListProps {
	offers: Offer[];
	selected: string[];
	onSelect: (selected: string[]) => void;
}

export function OrderBumpList({
	offers,
	selected,
	onSelect,
}: OrderBumpListProps) {
	if (!offers?.length) return null;

	const orderBumps = offers.filter((offer) => offer.type === 'ORDER_BUMP');
	if (!orderBumps.length) return null;

	const toggleOffer = (offerId: string) => {
		if (selected.includes(offerId)) {
			onSelect(selected.filter((id) => id !== offerId));
		} else {
			onSelect([...selected, offerId]);
		}
	};

	return (
		<div className='space-y-4'>
			{orderBumps.map((offer) => (
				<OrderBumpCard
					key={offer.id}
					offer={offer}
					checked={selected.includes(offer.id)}
					onToggle={(checked) => toggleOffer(offer.id)}
				/>
			))}
		</div>
	);
}
