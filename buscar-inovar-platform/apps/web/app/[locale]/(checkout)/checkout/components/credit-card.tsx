// apps/web/modules/saas/products/components/credit-card.tsx

import React from 'react';
import payment from 'payment';
import { cn } from '@ui/lib';

interface CreditCardProps {
	number?: string;
	name?: string;
	expiry?: string;
	cvc?: string;
	focused?: 'number' | 'name' | 'expiry' | 'cvc' | null;
	className?: string;
}

const getCardType = (number: string = '') => {
	const cleanNumber = number.replace(/\s+/g, '');
	return payment.fns.cardType(cleanNumber) || 'unknown';
};

export function CreditCard({
	number = '',
	name = '',
	expiry = '',
	cvc = '',
	focused = null,
	className,
}: CreditCardProps) {
	const cardType = getCardType(number);

	const getCardStyle = () => {
		switch (cardType) {
			case 'visa':
				return 'bg-gradient-to-tr from-blue-800 to-blue-400';
			case 'mastercard':
				return 'bg-gradient-to-tr from-red-800 to-red-400';
			case 'amex':
				return 'bg-gradient-to-tr from-teal-800 to-teal-400';
			case 'discover':
				return 'bg-gradient-to-tr from-orange-800 to-orange-400';
			case 'elo':
				return 'bg-gradient-to-tr from-emerald-800 to-emerald-400';
			default:
				return 'bg-gradient-to-tr from-gray-800 to-gray-400';
		}
	};

	const formatCardNumber = (num: string) => {
		const groups = num.replace(/\s+/g, '').match(/.{1,4}/g) || [];
		return groups.join(' ').padEnd(19, '•');
	};

	const formatName = (n: string) => (n ? n.toUpperCase() : 'NOME NO CARTÃO');
	const formatExpiry = (exp: string) => exp || 'MM/AA';

	return (
		<div
			className={cn(
				'relative h-[220px] w-full rounded-xl text-white shadow-xl transition-all duration-300',
				getCardStyle(),
				className
			)}
		>
			{/* Card Content */}
			<div className='absolute inset-0 p-6 flex flex-col justify-between'>
				{/* Chip */}
				<div className='flex justify-between'>
					<div className='w-12 h-9 bg-yellow-400/80 rounded-md flex items-center justify-center'>
						<div className='w-10 h-7 rounded border-2 border-yellow-600/50'></div>
					</div>

					{/* Card Type */}
					<div className='text-xl font-bold'>
						{cardType !== 'unknown' && cardType.toUpperCase()}
					</div>
				</div>

				{/* Card Number */}
				<div>
					<div
						className={cn(
							'font-mono text-xl tracking-wider',
							focused === 'number' && 'text-blue-200'
						)}
					>
						{formatCardNumber(number)}
					</div>
				</div>

				{/* Card Holder & Expiry */}
				<div className='flex justify-between'>
					<div>
						<div className='text-xs opacity-75'>TITULAR DO CARTÃO</div>
						<div
							className={cn(
								'font-mono tracking-wide',
								focused === 'name' && 'text-blue-200'
							)}
						>
							{formatName(name)}
						</div>
					</div>

					<div className='text-right'>
						<div className='text-xs opacity-75'>VALIDADE</div>
						<div
							className={cn(
								'font-mono tracking-wide',
								focused === 'expiry' && 'text-blue-200'
							)}
						>
							{formatExpiry(expiry)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
