// components/customer-form.tsx
import { User } from 'lucide-react';
import { Controller, useFormContext } from 'react-hook-form';

import { Card, CardContent, CardHeader, CardTitle } from '@ui/components/card';
import { Input } from '@ui/components/input';

import { CPFInput } from './cpf-input';
import { FormField } from './form-field';
import { InternationalPhoneInput } from './phone-input-international';
import { CheckoutFormData } from './types';

export function CustomerForm() {
	const {
		control,
		formState: { errors },
		setError,
		clearErrors,
	} = useFormContext<CheckoutFormData>();

	// Função para validar email
	const validateEmail = (email: string) => {
		if (!email.trim()) {
			clearErrors('customerData.email');
			return;
		}

		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(email)) {
			setError('customerData.email', { message: 'Email inválido' });
		} else {
			clearErrors('customerData.email');
		}
	};

	// Função para validar telefone
	const validatePhone = (phone: string) => {
		if (!phone.trim()) {
			clearErrors('customerData.phone');
			return;
		}

		const cleanPhone = phone.replace(/\D/g, '');
		if (cleanPhone.length < 10) {
			setError('customerData.phone', { message: 'Telefone deve ter pelo menos 10 dígitos' });
		} else {
			clearErrors('customerData.phone');
		}
	};

	// Função para validar nome
	const validateName = (name: string) => {
		if (!name.trim()) {
			clearErrors('customerData.name');
			return;
		}

		if (name.trim().length < 3) {
			setError('customerData.name', { message: 'Nome deve ter pelo menos 3 caracteres' });
		} else {
			clearErrors('customerData.name');
		}
	};

	// Função para validar CPF
	const validateCPF = (cpf: string) => {
		if (!cpf.trim()) {
			clearErrors('customerData.cpf');
			return;
		}

		const cleanCPF = cpf.replace(/\D/g, '');
		if (cleanCPF.length !== 11) {
			setError('customerData.cpf', { message: 'CPF deve ter 11 dígitos' });
		} else {
			clearErrors('customerData.cpf');
		}
	};

	return (
		<Card className='bg-white shadow-sm'>
			<CardHeader className='p-6 space-y-1'>
				<div className='flex items-center gap-2'>
					<User className='h-5 w-5 text-muted-foreground' />
					<CardTitle className='text-lg'>Informações</CardTitle>
				</div>
				<p className='text-sm text-muted-foreground'>
					Preencha seus dados para completar a compra
				</p>
			</CardHeader>

			<CardContent className='p-6 pt-0 space-y-4'>
				<div className='grid gap-4 md:grid-cols-2'>
					<FormField
						name='customerData.name'
						label='Nome completo'
						error={errors.customerData?.name?.message}
					>
						<Controller
							name='customerData.name'
							control={control}
							render={({ field }) => (
								<Input
									{...field}
									placeholder='Digite seu nome completo'
									onBlur={(e) => {
										field.onBlur();
										validateName(e.target.value);
									}}
								/>
							)}
						/>
					</FormField>

					<FormField
						name='customerData.email'
						label='E-mail'
						error={errors.customerData?.email?.message}
					>
						<Controller
							name='customerData.email'
							control={control}
							render={({ field }) => (
								<Input
									{...field}
									type='email'
									placeholder='<EMAIL>'
									onChange={(e) => field.onChange(e.target.value)}
									onBlur={(e) => {
										field.onBlur();
										validateEmail(e.target.value);
									}}
								/>
							)}
						/>
					</FormField>

					<FormField
						name='customerData.cpf'
						label='CPF'
						error={errors.customerData?.cpf?.message}
					>
						<Controller
							name='customerData.cpf'
							control={control}
							render={({ field }) => (
								<CPFInput
									value={field.value}
									onChange={field.onChange}
									onBlur={(e) => {
										field.onBlur();
										validateCPF(field.value);
									}}
									placeholder='000.000.000-00'
								/>
							)}
						/>
					</FormField>

					<FormField
						name='customerData.phone'
						label='Celular'
						error={errors.customerData?.phone?.message}
					>
						<Controller
							name='customerData.phone'
							control={control}
							render={({ field }) => (
								<InternationalPhoneInput
									value={field.value}
									onChange={field.onChange}
									onBlur={(e) => {
										field.onBlur();
										validatePhone(field.value);
									}}
									error={!!errors.customerData?.phone}
									errorMessage={errors.customerData?.phone?.message}
								/>
							)}
						/>
					</FormField>
				</div>
			</CardContent>
		</Card>
	);
}
