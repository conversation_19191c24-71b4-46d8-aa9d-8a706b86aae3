import { Card, CardContent, CardHeader } from '@ui/components/card';
// components/success-header.tsx
import { CheckCircle2 } from 'lucide-react';
import { formatCurrency } from 'utils';

interface SuccessHeaderProps {
	order: {
		id: string;
		amount: number;
		paymentMethod: string;
		customerData: {
			name: string;
			email: string;
		};
	};
}

export function SuccessHeader({ order }: SuccessHeaderProps) {
	console.log('order SuccessHeader', order);

	return (
		<Card className='mb-8'>
			<CardHeader className='text-center pb-6'>
				<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
					<CheckCircle2 className='h-6 w-6 text-primary' />
				</div>
				<h1 className='text-2xl font-semibold'>Pagamento Confirmado!</h1>
				<p className='text-muted-foreground'>
					{order.paymentMethod === 'CREDIT_CARD'
						? 'Sua compra foi processada com sucesso'
						: 'Recebemos a confirmação do seu pagamento'}
				</p>
			</CardHeader>

			<CardContent className='border-t pt-6'>
				<div className='space-y-4'>
					<div className='flex justify-between text-sm'>
						<span className='text-muted-foreground'>Número do pedido</span>
						<span className='font-medium'>{order.id}</span>
					</div>

					<div className='flex justify-between text-sm'>
						<span className='text-muted-foreground'>Total pago</span>
						<span className='font-medium'>{formatCurrency(order.amount)}</span>
					</div>

					<div className='flex justify-between text-sm'>
						<span className='text-muted-foreground'>Email</span>
						<span className='font-medium'>{order?.user?.email}</span>
					</div>

					<div className='rounded-lg bg-muted p-4 text-sm text-center text-muted-foreground'>
						Enviamos um email para você com todos os detalhes da sua compra
					</div>
				</div>
			</CardContent>
		</Card>
	);
}
