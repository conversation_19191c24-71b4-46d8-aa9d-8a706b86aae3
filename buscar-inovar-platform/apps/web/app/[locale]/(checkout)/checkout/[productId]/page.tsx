// app/[locale]/(checkout)/checkout/[productId]/page.tsx
import "server-only";
import { redirect } from 'next/navigation';
import { db } from 'database';
import { CheckoutForm } from '../components/checkout-form';
import { CheckoutHeader } from '../components/checkout-header';

interface CheckoutPageProps {
	params: Promise<{
		productId: string;
	}>;
}

export default async function CheckoutPage({ params }: CheckoutPageProps) {
	const { productId } = await params;

	if (!productId) {
		redirect('/');
	}

	try {
		const product = await db.product.findFirst({
			where: {
				id: productId,
				status: 'PUBLISHED',
			},
		});

		if (!product) {
			redirect('/');
		}

		// Buscar creator e offers separadamente
		const [creator, offers] = await Promise.all([
			db.user.findUnique({
				where: { id: product.creatorId },
				select: { id: true, name: true },
			}),
			db.offer.findMany({
				where: {
					productId: product.id,
					active: true,
					type: 'ORDER_BUMP',
				},
				select: {
					id: true,
					title: true,
					description: true,
					price: true,
					type: true,
				},
			}),
		]);

		console.log('product', {
			id: product.id,
			checkoutSettings: product.checkoutSettings,
			metadata: product.metadata,
		});

		// Handle external checkout redirection
		if (product.checkoutType === 'EXTERNAL' && product.customCheckoutUrl) {
			redirect(product.customCheckoutUrl);
		}

		return (
			<div className='min-h-screen bg-gray-50/50'>
				<CheckoutHeader />

				<div className='mx-3 md:mx-auto md:container py-5  '>
					{/* Display a banner if available from checkoutSettings or metadata */}
					{(() => {
						// Try to get banner URL from different possible locations
						let bannerUrl: string | null = null;

						// Check in checkoutSettings
						if (
							product.checkoutSettings &&
							typeof product.checkoutSettings === 'object'
						) {
							const settings = product.checkoutSettings as Record<
								string,
								unknown
							>;
							if (typeof settings.banner === 'string') {
								bannerUrl = settings.banner;
							}
						}

						// Check in metadata if not found in settings
						if (
							!bannerUrl &&
							product.metadata &&
							typeof product.metadata === 'object'
						) {
							const metadata = product.metadata as Record<string, unknown>;
							if (typeof metadata.checkoutBanner === 'string') {
								bannerUrl = metadata.checkoutBanner;
							}
						}

						// Return banner element if URL is found
						return bannerUrl ? (
							<div className='w-full mb-6'>
								<img
									src={bannerUrl}
									alt='Checkout banner'
									className='w-full rounded-lg shadow-sm'
								/>
							</div>
						) : null;
					})()}
					<CheckoutForm
						product={{
							id: product.id,
							title: product.title,
							description: product.description,
							type: product.type,
							price: Number(product.price),
							installmentsLimit: product.installmentsLimit,
							enableInstallments: product.enableInstallments,
							thumbnail: product.thumbnail,
							checkoutType: product.checkoutType,
							acceptedPayments: product.acceptedPayments || [
								'CREDIT_CARD',
								'PIX',
							],
							checkoutSettings: product.checkoutSettings,
							customCheckoutUrl: product.customCheckoutUrl,
							successUrl: product.successUrl,
							cancelUrl: product.cancelUrl,
							termsUrl: product.termsUrl,
							offers: offers?.map((o) => ({
								id: o.id,
								title: o.title,
								description: o.description,
								price: Number(o.price),
								type: o.type,
							})),
						}}
					/>
				</div>
			</div>
		);
	} catch (error) {
		console.error('Error loading product for checkout:', error);
		redirect('/');
	}
}
