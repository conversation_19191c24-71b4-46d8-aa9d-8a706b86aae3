{"[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "tailwindCSS.experimental.classRegex": [["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "explicit"}, "typescript.preferences.importModuleSpecifier": "shortest", "typescript.tsdk": "node_modules/typescript/lib", "i18n-ally.localesPaths": ["packages/i18n/translations"], "i18n-ally.keystyle": "nested", "i18n-ally.enabledFrameworks": ["next-intl"]}