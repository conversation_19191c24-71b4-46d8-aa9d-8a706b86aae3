# Instruções para Desenvolvimento da Plataforma Buscar Inovar

## **Visão Geral**
O objetivo é adaptar o **Supastarter** para criar a plataforma **Buscar Inovar**, uma plataforma educacional com funcionalidades como cursos online, mentorias, co-produções, split de pagamentos, marketplace de produtos e afiliados. O projeto segue a estrutura monorepo do Supastarter, utilizando Next.js 14, Prisma, Asaas (pagamentos) e Bunny.net (vídeos).

---

## **Estrutura do Projeto**
O projeto está organizado em monorepo com a seguinte estrutura:

```
├── apps/web/              # Aplicação Next.js
│   ├── app/              # App Router
│   │   ├── (auth)/       # Autenticação (já implementado)
│   │   ├── (marketing)/  # Landing pages (já implementado)
│   │   └── (saas)/       # Área logada (adaptar)
│   └── modules/          # Módulos da aplicação
├── packages/
│   ├── api/              # API routes e procedures
│   ├── auth/             # Sistema de autenticação
│   ├── database/         # Schema Prisma e client
│   ├── mail/             # Templates de email
│   └── storage/          # Gerenciamento de arquivos
```

---

## **Integrações Principais**

### **1. Pagamentos (Asaas)**
- **Funcionalidades:**
  - Split de pagamentos para co-produtores.
  - Gestão de assinaturas.
  - Checkout transparente.
  - Geração de links de pagamento.
- **Implementação:**
  - Configurar a API REST do Asaas no diretório `packages/api/modules/billing`.
  - Criar procedures para gerenciar assinaturas, splits e links de pagamento.
  - Adaptar o checkout na área de membros (`apps/web/app/(saas)/checkout`).

### **2. Vídeos (Bunny.net)**
- **Funcionalidades:**
  - Upload e streaming de vídeos.
  - Proteção de conteúdo.
  - Analytics de visualização.
- **Implementação:**
  - Configurar o Bunny.net no diretório `packages/storage/provider/bunny`.
  - Criar procedures para upload e gerenciamento de vídeos.
  - Integrar o player de vídeo nas páginas de cursos (`apps/web/app/(saas)/courses`).

---

## **Banco de Dados**
O schema Prisma já está estendido com os modelos necessários. Verifique e ajuste os seguintes modelos:

- **Product**: Cursos e mentorias.
- **Course**: Estrutura do curso.
- **TeacherProfile**: Perfil do professor.
- **AffiliateProfile**: Perfil do afiliado.
- **Order**: Pedidos e pagamentos.
- **CourseEnrollment**: Matrículas.
- **Review**: Avaliações.
- **CoProducer**: Co-produtores.

---

## **Funcionalidades a Serem Implementadas**

### **1. Sistema de Cursos Online**
- **Páginas:**
  - Listagem de cursos (`apps/web/app/(saas)/courses`).
  - Detalhes do curso (`apps/web/app/(saas)/courses/[id]`).
  - Player de vídeo integrado com Bunny.net.
- **Funcionalidades:**
  - Upload de vídeos e materiais.
  - Emissão de certificados.
  - Progresso do aluno.

### **2. Área de Membros**
- **Páginas:**
  - Dashboard (`apps/web/app/(saas)/dashboard`).
  - Cursos matriculados (`apps/web/app/(saas)/courses`).
  - Configurações de perfil (`apps/web/app/(saas)/settings`).
- **Funcionalidades:**
  - Acesso gratuito e pago.
  - Gerenciamento de assinaturas via Asaas.

### **3. Sistema de Mentorias**
- **Páginas:**
  - Agendamento de mentorias (`apps/web/app/(saas)/mentoring`).
  - Calendário integrado.
- **Funcionalidades:**
  - Integração com ferramentas de videoconferência (ex: Zoom ou Google Meet).
  - Notificações por email.

### **4. Co-produções e Split de Pagamentos**
- **Páginas:**
  - Gerenciamento de co-produtores (`apps/web/app/(saas)/products/[id]/edit`).
- **Funcionalidades:**
  - Split de pagamentos via Asaas.
  - Controle de royalties.

### **5. Marketplace de Produtos com Afiliados**
- **Páginas:**
  - Listagem de produtos (`apps/web/app/(saas)/products`).
  - Painel do afiliado (`apps/web/app/(saas)/affiliates`).
- **Funcionalidades:**
  - Cadastro de afiliados.
  - Geração de links de afiliados.
  - Relatórios de desempenho.

### **6. Cadastro e Aprovação de Professores**
- **Páginas:**
  - Formulário de cadastro (`apps/web/app/(saas)/onboarding`).
  - Painel de aprovação (`apps/web/app/(saas)/admin/teachers`).
- **Funcionalidades:**
  - Verificação de documentos.
  - Aprovação manual ou automática.

---

## **Boas Práticas**

### **1. Padrões de Código**
- Manter a arquitetura base do Supastarter.
- Seguir convenções de nomenclatura e estrutura de pastas.
- Utilizar TypeScript para type-safety.

### **2. Segurança**
- Validar entradas de usuários.
- Proteger rotas sensíveis com autenticação.
- Utilizar HTTPS e sanitização de dados.

### **3. Performance**
- Otimizar consultas ao banco de dados.
- Utilizar caching onde necessário.
- Minimizar o tamanho dos assets.

### **4. Testes**
- Escrever testes unitários e de integração.
- Utilizar Cypress para testes E2E.

---

## **Fluxo de Trabalho**

1. **Configuração Inicial:**
   - Clonar o repositório do Supastarter.
   - Configurar variáveis de ambiente (`.env`).
   - Executar migrações do Prisma (`npx prisma migrate dev`).

2. **Desenvolvimento:**
   - Criar branches para cada funcionalidade.
   - Seguir a estrutura de pastas existente.
   - Documentar alterações no README.md.

3. **Testes:**
   - Executar testes antes de cada commit.
   - Verificar integrações com Asaas e Bunny.net.

4. **Deploy:**
   - Configurar CI/CD (ex: Vercel ou GitHub Actions).
   - Realizar deploy em ambiente de staging antes de produção.

---

## **Referências**
- [Documentação do Supastarter](https://supastarter.dev/docs/nextjs)
- [Documentação do Asaas](https://asaas.com/docs)
- [Documentação do Bunny.net](https://bunny.net/docs)
- [Documentação do Next.js](https://nextjs.org/docs)
